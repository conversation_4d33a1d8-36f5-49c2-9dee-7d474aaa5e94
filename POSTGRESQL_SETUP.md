# 🐘 PostgreSQL Setup Guide for HIV Predictor App

Your app has been migrated from SQLite to PostgreSQL! This guide will help you set up your PostgreSQL database.

## 🚀 Quick Setup (Recommended)

### Option 1: Automated Setup
```bash
# Run the setup script
python setup_postgresql.py

# Start the servers (will auto-configure everything)
start_servers.bat
```

### Option 2: Manual Setup with Supabase (Free)

1. **Create Supabase Account**
   - Go to [supabase.com](https://supabase.com)
   - Sign up for free account
   - Create a new project

2. **Get Database Credentials**
   - Go to Settings → Database
   - Copy the connection string (looks like):
   ```
   postgresql://postgres.your-project-ref:<EMAIL>:6543/postgres
   ```

3. **Configure Your App**
   - Create `flask_backend/.env` file:
   ```env
   DATABASE_URL=your-connection-string-here
   SECRET_KEY=your-secret-key
   FLASK_ENV=development
   ```

4. **Initialize Database**
   ```bash
   cd flask_backend
   pip install psycopg2-binary python-dotenv
   python init_database.py
   python app.py
   ```

## 📋 What Changed

### ✅ Removed
- ❌ SQLite database file (`hiv_predictor.db`)
- ❌ SQLite-specific configuration
- ❌ File-based database limitations

### ✅ Added
- ✅ PostgreSQL support with psycopg2
- ✅ Environment variable configuration
- ✅ Automatic database initialization
- ✅ Production-ready database setup
- ✅ Better error handling and connection testing

## 🔧 Configuration Options

### Environment Variables (.env file)
```env
# Required: Database connection
DATABASE_URL=postgresql://user:password@host:port/database

# Optional: Individual components (alternative to DATABASE_URL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hiv_predictor
DB_USER=postgres
DB_PASSWORD=your-password

# Application settings
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
```

## 🏥 Database Providers

### 1. 🥇 Supabase (Recommended)
- **Free Tier**: 500MB storage, 2M row reads/month
- **Features**: Real-time, built-in auth, dashboard
- **Setup**: Easiest, web-based interface
- **Cost**: Free → $25/month

### 2. 🥈 Railway
- **Free Tier**: Limited but includes hosting
- **Features**: Auto-deploy, monitoring
- **Setup**: Git-based deployment
- **Cost**: $5/month (includes app hosting)

### 3. 🥉 Local PostgreSQL
- **Free**: Completely free
- **Features**: Full control, no limits
- **Setup**: Requires PostgreSQL installation
- **Cost**: Free (but requires maintenance)

## 🧪 Testing Your Setup

### 1. Test Database Connection
```bash
cd flask_backend
python init_database.py
```

### 2. Test API Health
```bash
curl http://localhost:5001/api/health
```

Expected response:
```json
{
  "status": "healthy",
  "message": "HIV Predictor API is running",
  "database": "connected",
  "database_type": "PostgreSQL",
  "model_loaded": true
}
```

### 3. Test Admin Login
- Username: `admin`
- Password: `admin123`

## 🔒 Security Notes

### Production Checklist
- [ ] Change default admin password
- [ ] Use strong SECRET_KEY
- [ ] Enable SSL/TLS for database connection
- [ ] Use environment variables for sensitive data
- [ ] Don't commit .env file to version control

### .gitignore Addition
Add to your `.gitignore`:
```
flask_backend/.env
*.env
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Connection Failed
```
❌ Database connection failed: could not connect to server
```
**Solutions:**
- Check your connection string
- Verify database server is running
- Check firewall settings
- Ensure SSL mode is correct for cloud databases

#### 2. Module Not Found
```
❌ ModuleNotFoundError: No module named 'psycopg2'
```
**Solution:**
```bash
pip install psycopg2-binary
```

#### 3. SSL Required
```
❌ SSL connection required
```
**Solution:** Add `?sslmode=require` to your connection string

#### 4. Permission Denied
```
❌ permission denied for database
```
**Solution:** Check username/password and database permissions

### Getting Help

1. **Check Logs**: Look at the Flask server output for detailed error messages
2. **Test Connection**: Use `python init_database.py` to test your setup
3. **Verify Credentials**: Double-check your .env file configuration

## 📊 Benefits of PostgreSQL

### vs SQLite
- ✅ **Multi-user**: Concurrent access from multiple devices
- ✅ **Scalable**: Handle thousands of users and predictions
- ✅ **Remote Access**: Access from anywhere
- ✅ **Backup**: Automatic backups and point-in-time recovery
- ✅ **Security**: Advanced security features and encryption
- ✅ **JSON Support**: Native JSON columns for complex data

### Healthcare-Specific Benefits
- ✅ **ACID Compliance**: Data integrity for medical records
- ✅ **Audit Trails**: Track all data changes
- ✅ **Encryption**: Data encryption at rest and in transit
- ✅ **Compliance**: HIPAA-compliant hosting options
- ✅ **Reliability**: Enterprise-grade reliability and uptime

## 🎉 You're All Set!

Your HIV Predictor app now uses PostgreSQL and is ready for production use! 

**Next Steps:**
1. Test the app with the new database
2. Create some test users and predictions
3. Check the admin dashboard
4. Consider setting up automated backups

**Need Help?** Run `python setup_postgresql.py` for guided setup assistance.
