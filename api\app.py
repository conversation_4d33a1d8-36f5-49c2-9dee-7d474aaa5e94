from flask import Flask, request, jsonify
from flask_cors import CORS
from werkzeug.exceptions import HTTPException

app = Flask(__name__)
# Add proper CORS configuration
CORS(app, resources={r"/*": {"origins": "*"}})

# Add global error handlers
@app.errorhandler(Exception)
def handle_exception(e):
    # Pass through HTTP errors
    if isinstance(e, HTTPException):
        response = e.get_response()
        response.data = jsonify({
            "code": e.code,
            "name": e.name,
            "error": e.description,
        }).data
        response.content_type = "application/json"
        return response

    # Handle non-HTTP exceptions
    return jsonify({
        "error": "Internal Server Error",
        "message": str(e)
    }), 500

# Simple rule-based prediction for now (we'll improve this later)
def simple_prediction(symptoms):
    """
    Enhanced rule-based HIV risk assessment with Yes/No/Sometimes options
    Symptom values: 0=No, 1=Sometimes, 2=Yes
    Returns risk level based on symptom severity and risk factors
    """
    # Calculate weighted symptom score (0=No, 1=Sometimes, 2=Yes)
    # Original physical symptoms
    symptom_keys = ['fever', 'headache', 'skin_rash', 'muscle_pain', 'weight_loss',
                   'fatigue', 'oral_ulcers', 'swollen_lymph_nodes', 'diarrhea', 'night_sweats',
                   # New early/acute HIV symptoms
                   'sore_throat', 'joint_pain', 'nausea', 'loss_of_appetite', 'chills', 'persistent_cough',
                   # New advanced/chronic HIV symptoms
                   'recurring_infections', 'memory_problems', 'vision_problems', 'persistent_headaches']

    symptom_score = sum(symptoms.get(key, 0) for key in symptom_keys)
    max_symptom_score = len(symptom_keys) * 2  # Maximum possible score

    # Calculate risk behavior score
    risk_behavior_keys = ['unprotected_sex', 'shared_needles', 'multiple_partners',
                         'blood_transfusion', 'tattoo_piercing_unsterile', 'partner_hiv_positive']
    risk_behavior_score = sum(symptoms.get(key, 0) for key in risk_behavior_keys)
    max_risk_score = len(risk_behavior_keys) * 2  # Maximum possible score

    # Normalize scores (0-1 range)
    normalized_symptoms = symptom_score / max_symptom_score if max_symptom_score > 0 else 0
    normalized_risks = risk_behavior_score / max_risk_score if max_risk_score > 0 else 0

    # Calculate overall risk score (risk behaviors weighted more heavily)
    overall_risk = (normalized_symptoms * 0.3) + (normalized_risks * 0.7)

    # Determine risk level and confidence
    if overall_risk >= 0.6:
        return "High Risk", min(0.95, 0.6 + (overall_risk * 0.3))
    elif overall_risk >= 0.3:
        return "Medium Risk", min(0.8, 0.4 + (overall_risk * 0.4))
    else:
        return "Low Risk", max(0.1, overall_risk * 0.5)

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'API is running',
        'model_loaded': True
    })

@app.route('/predict', methods=['POST'])
def predict():
    try:
        # Get data from request
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Handle both old format (direct symptoms) and new format (with locations)
        if 'symptoms' in data:
            # New format with locations
            symptoms = data['symptoms']
            exposure_locations = data.get('exposure_locations', {})
        else:
            # Old format (direct symptoms)
            symptoms = data
            exposure_locations = {}

        # Expected symptom order - Enhanced with additional symptoms
        symptom_order = [
            # Original physical symptoms
            'fever', 'headache', 'skin_rash', 'muscle_pain', 'weight_loss',
            'fatigue', 'oral_ulcers', 'swollen_lymph_nodes', 'diarrhea', 'night_sweats',
            # New early/acute HIV symptoms
            'sore_throat', 'joint_pain', 'nausea', 'loss_of_appetite', 'chills', 'persistent_cough',
            # New advanced/chronic HIV symptoms
            'recurring_infections', 'memory_problems', 'vision_problems', 'persistent_headaches',
            # Original risk behaviors
            'unprotected_sex', 'shared_needles', 'multiple_partners',
            # New risk factors
            'blood_transfusion', 'tattoo_piercing_unsterile', 'partner_hiv_positive'
        ]

        # Validate all required symptoms are present
        missing_symptoms = [symptom for symptom in symptom_order if symptom not in symptoms]
        if missing_symptoms:
            return jsonify({
                'error': f'Missing symptoms: {", ".join(missing_symptoms)}'
            }), 400

        # Use enhanced rule-based prediction
        prediction, confidence = simple_prediction(symptoms)

        # Prepare response
        response_data = {
            'prediction': prediction,
            'confidence': float(confidence),
            'features_used': symptom_order
        }

        # Add exposure locations if provided
        if exposure_locations:
            response_data['exposure_locations'] = exposure_locations

            # Add location-based risk assessment
            high_risk_locations = []
            for behavior, location in exposure_locations.items():
                if location and behavior in ['unprotected_sex', 'shared_needles', 'multiple_partners']:
                    high_risk_locations.append(f"{behavior.replace('_', ' ')}: {location}")

            if high_risk_locations:
                response_data['location_warning'] = {
                    'message': 'High-risk exposure locations reported',
                    'locations': high_risk_locations,
                    'recommendation': 'Please consider getting tested and informing healthcare providers about these locations for contact tracing.'
                }

        return jsonify(response_data)

    except Exception as e:
        return jsonify({'error': str(e)}), 400

if __name__ == '__main__':
    # Use environment variable to control debug mode
    import os
    debug_mode = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    print("🚀 API Server Starting...")
    print("🌐 Server: http://localhost:5000")
    print("✅ CORS enabled for Flutter app")
    print(f"🐞 Debug Mode: {'Enabled' if debug_mode else 'Disabled'}")
    
    app.run(debug=True, host='0.0.0.0', port=5000) 
