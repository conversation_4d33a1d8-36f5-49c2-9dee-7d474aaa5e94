# HIV Predictor Backend Requirements

## 🎯 Overview

To enable AI-powered predictions in the HIV Predictor app, you need to set up backend servers with machine learning models.

## 🏗️ Backend Architecture

### Flask Server (Primary - Port 5001)

```
http://localhost:5001/
├── /api/health          # Health check endpoint
├── /api/predict         # Main prediction endpoint
└── /api/predict-batch   # Batch predictions (optional)
```

### API Server (Backup - Port 5000)

```
http://localhost:5000/
├── /health              # Health check endpoint
├── /predict             # Prediction endpoint
└── /batch-predict       # Batch predictions (optional)
```

## 📊 Required API Endpoints

### 1. Health Check Endpoint

**GET** `/api/health` (Flask) or `/health` (API Server)

**Response:**

```json
{
  "status": "healthy",
  "model_loaded": true,
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0"
}
```

### 2. Prediction Endpoint

**POST** `/api/predict` (Flask) or `/predict` (API Server)

**Request Body:**

```json
{
  "fever": 0, // 0=No, 1=Sometimes, 2=Yes
  "headache": 1,
  "skin_rash": 0,
  "muscle_pain": 2,
  "weight_loss": 0,
  "fatigue": 1,
  "oral_ulcers": 0,
  "swollen_lymph_nodes": 0,
  "diarrhea": 0,
  "night_sweats": 0,
  "unprotected_sex": 2,
  "shared_needles": 0,
  "multiple_partners": 1
}
```

**Response:**

```json
{
  "prediction": "Medium Risk",
  "confidence": 0.78,
  "features_used": [
    "fever",
    "muscle_pain",
    "fatigue",
    "unprotected_sex",
    "multiple_partners"
  ],
  "risk_score": 45.2,
  "recommendations": [
    "Schedule HIV testing within 2-4 weeks",
    "Practice safe behaviors",
    "Consider regular testing if at ongoing risk"
  ]
}
```

### 3. Prediction with Locations (Enhanced)

**POST** `/api/predict` with location data

**Request Body:**

```json
{
  "symptoms": {
    "fever": 0,
    "headache": 1
    // ... other symptoms
  },
  "exposure_locations": {
    "unprotected_sex": "City Center Club",
    "shared_needles": "",
    "multiple_partners": "Downtown Area"
  },
  "user_id": 123
}
```

**Response:**

```json
{
  "prediction": "High Risk",
  "confidence": 0.89,
  "features_used": ["unprotected_sex", "multiple_partners"],
  "exposure_locations": {
    "unprotected_sex": "City Center Club",
    "multiple_partners": "Downtown Area"
  },
  "location_warning": "Consider informing healthcare providers about exposure locations for contact tracing if needed.",
  "risk_score": 78.5
}
```

## 🧠 Machine Learning Model Specifications

### Input Features (22 total) - ENHANCED DATASET

1. **Physical Symptoms (16):**

   - **Original symptoms (10):** fever, headache, skin_rash, muscle_pain, weight_loss, fatigue, oral_ulcers, swollen_lymph_nodes, diarrhea, night_sweats
   - **New early/acute symptoms (6):** sore_throat, joint_pain, nausea, loss_of_appetite, chills, persistent_cough

2. **Advanced Symptoms (4):**

   - recurring_infections, memory_problems, vision_problems, persistent_headaches

3. **Risk Behaviors (6):**
   - **Original behaviors (3):** unprotected_sex, shared_needles, multiple_partners
   - **New risk factors (3):** blood_transfusion, tattoo_piercing_unsterile, partner_hiv_positive

### Feature Encoding

- **Values**: 0 (No), 1 (Sometimes), 2 (Yes)
- **Type**: Integer encoding
- **Range**: [0, 2] for each feature

### Output Requirements

- **Prediction**: String ("Low Risk", "Medium Risk", "High Risk")
- **Confidence**: Float [0.0, 1.0]
- **Risk Score**: Float [0.0, 100.0] (optional)

### Model Performance Targets

- **Accuracy**: >85% on validation set
- **Precision**: >80% for High Risk class
- **Recall**: >90% for High Risk class
- **Response Time**: <2 seconds per prediction

## 🛠️ Technical Requirements

### Python Dependencies

```txt
flask>=2.0.0
scikit-learn>=1.0.0
pandas>=1.3.0
numpy>=1.21.0
joblib>=1.1.0
flask-cors>=3.0.0
```

### Model Training Data Format - ENHANCED

```csv
fever,headache,skin_rash,muscle_pain,weight_loss,fatigue,oral_ulcers,swollen_lymph_nodes,diarrhea,night_sweats,sore_throat,joint_pain,nausea,loss_of_appetite,chills,persistent_cough,recurring_infections,memory_problems,vision_problems,persistent_headaches,unprotected_sex,shared_needles,multiple_partners,blood_transfusion,tattoo_piercing_unsterile,partner_hiv_positive,risk_level
0,1,0,2,0,1,0,0,0,0,1,0,1,0,0,0,0,0,0,0,2,0,1,0,0,0,Medium Risk
2,2,1,2,1,2,1,1,1,2,2,1,2,1,2,1,1,1,0,1,2,2,2,0,1,2,High Risk
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,Low Risk
1,1,0,1,0,1,0,0,0,1,1,1,0,0,1,0,0,0,0,0,1,0,0,0,0,1,Medium Risk
```

### Recommended ML Algorithms

1. **Random Forest** (Recommended)
2. **Gradient Boosting** (XGBoost/LightGBM)
3. **Logistic Regression** (Baseline)
4. **Neural Networks** (Advanced)

## 🚀 Quick Start Implementation

### Flask Server Example (app.py)

```python
from flask import Flask, request, jsonify
from flask_cors import CORS
import joblib
import numpy as np

app = Flask(__name__)
CORS(app)

# Load trained model
model = joblib.load('hiv_risk_model.pkl')

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'model_loaded': model is not None,
        'timestamp': datetime.utcnow().isoformat() + 'Z'
    })

@app.route('/api/predict', methods=['POST'])
def predict():
    try:
        data = request.json

        # Extract features in correct order
        features = [
            data.get('fever', 0),
            data.get('headache', 0),
            data.get('skin_rash', 0),
            data.get('muscle_pain', 0),
            data.get('weight_loss', 0),
            data.get('fatigue', 0),
            data.get('oral_ulcers', 0),
            data.get('swollen_lymph_nodes', 0),
            data.get('diarrhea', 0),
            data.get('night_sweats', 0),
            data.get('unprotected_sex', 0),
            data.get('shared_needles', 0),
            data.get('multiple_partners', 0)
        ]

        # Make prediction
        prediction = model.predict([features])[0]
        confidence = model.predict_proba([features]).max()

        return jsonify({
            'prediction': prediction,
            'confidence': float(confidence),
            'features_used': [k for k, v in data.items() if v > 0]
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
```

## 📈 Model Training Pipeline

### 1. Data Collection

- Collect labeled HIV risk assessment data
- Ensure balanced dataset across risk levels
- Include diverse demographic representation

### 2. Data Preprocessing

- Handle missing values
- Encode categorical variables
- Feature scaling (if needed)
- Train/validation/test split (70/15/15)

### 3. Model Training

```python
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report

# Load and prepare data
X = df[feature_columns]
y = df['risk_level']

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.3, random_state=42, stratify=y
)

# Train model
model = RandomForestClassifier(
    n_estimators=100,
    max_depth=10,
    random_state=42
)
model.fit(X_train, y_train)

# Evaluate
predictions = model.predict(X_test)
print(classification_report(y_test, predictions))

# Save model
joblib.dump(model, 'hiv_risk_model.pkl')
```

### 4. Model Validation

- Cross-validation accuracy >85%
- Confusion matrix analysis
- Feature importance analysis
- Bias testing across demographics

## 🔧 Deployment Options

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Run Flask server
python app.py
```

### Docker Deployment

```dockerfile
FROM python:3.9-slim
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5001
CMD ["python", "app.py"]
```

### Cloud Deployment

- **AWS**: EC2 + Load Balancer
- **Google Cloud**: Cloud Run
- **Azure**: Container Instances
- **Heroku**: Web Dyno

## 🔒 Security & Privacy

### Data Protection

- Encrypt data in transit (HTTPS)
- No persistent storage of user data
- Anonymize logs
- GDPR compliance

### API Security

- Rate limiting
- Input validation
- CORS configuration
- Authentication (optional)

## 📊 Monitoring & Logging

### Metrics to Track

- Prediction accuracy
- Response times
- Error rates
- Model drift

### Logging Requirements

- Request/response logging
- Error tracking
- Performance metrics
- Model version tracking

## 🎯 Success Criteria

### Performance Benchmarks

- **Accuracy**: Match or exceed rule-based system (>80%)
- **Speed**: <2 seconds response time
- **Availability**: >99% uptime
- **Scalability**: Handle 1000+ requests/hour

### User Experience

- Seamless switching between AI/offline modes
- Clear confidence indicators
- Consistent prediction quality
- No service interruptions
