-- MySQL Database Setup for HIV Predictor App
-- Run this script in MySQL to create the database and tables

-- Create database
CREATE DATABASE IF NOT EXISTS hiv_predictor;
USE hiv_predictor;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100) NOT NULL,
  full_name VARCHAR(100) NOT NULL,
  created_at DATETIME NOT NULL,
  is_admin BOOLEAN DEFAULT FALSE
);

-- Create prediction_results table
CREATE TABLE IF NOT EXISTS prediction_results (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  prediction VARCHAR(50) NOT NULL,
  confidence DECIMAL(5,4) NOT NULL,
  symptoms TEXT NOT NULL,
  exposure_locations TEXT,
  location_warning TEXT,
  created_at DATETIME NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT IGNORE INTO users (username, password, email, full_name, created_at, is_admin)
VALUES ('admin', 'admin123', '<EMAIL>', 'System Administrator', NOW(), TRUE);

-- Show tables
SHOW TABLES;

-- Show admin user
SELECT * FROM users WHERE username = 'admin';

PRINT 'Database setup completed successfully!';
PRINT 'Default admin user: admin / admin123';
