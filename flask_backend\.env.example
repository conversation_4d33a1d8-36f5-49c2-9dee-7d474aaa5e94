# PostgreSQL Database Configuration
# Copy this file to .env and fill in your actual database credentials

# Supabase PostgreSQL Connection (Recommended)
DATABASE_URL=postgresql://username:password@host:port/database

# Alternative format for individual components
DB_HOST=your-supabase-host.supabase.co
DB_PORT=5432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=your-password

# Application Settings
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Example Supabase URL format:
# DATABASE_URL=postgresql://postgres.your-project-ref:<EMAIL>:6543/postgres
