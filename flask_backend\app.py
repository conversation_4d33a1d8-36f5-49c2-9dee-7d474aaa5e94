from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from datetime import datetime, timezone
import hashlib
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)  # Enable CORS for Flutter web

# Database configuration
def get_database_url():
    """Get database URL from environment variables with fallback to SQLite for demo"""
    database_url = os.getenv('DATABASE_URL')

    if database_url and database_url.startswith('postgresql://'):
        # Handle Supabase connection pooler URLs
        if 'pooler.supabase.com' in database_url and '?sslmode=' not in database_url:
            database_url += '?sslmode=require'
        return database_url

    # Check if PostgreSQL components are provided
    db_host = os.getenv('DB_HOST')
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')

    if db_host and db_user and db_password:
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'hiv_predictor')
        return f'postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}'

    # Fallback to SQLite for demo (when PostgreSQL is not configured)
    print("⚠️  PostgreSQL not configured, using SQLite fallback for demo")
    print("💡 To use PostgreSQL, run: python setup_postgresql.py")
    basedir = os.path.abspath(os.path.dirname(__file__))
    return f'sqlite:///{os.path.join(basedir, "demo.db")}'

app.config['SQLALCHEMY_DATABASE_URI'] = get_database_url()
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')

db = SQLAlchemy(app)

# Database Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(100), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    is_admin = db.Column(db.Boolean, default=False)
    
    # Relationship
    predictions = db.relationship('PredictionResult', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'created_at': self.created_at.isoformat(),
            'is_admin': self.is_admin
        }

class PredictionResult(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    prediction = db.Column(db.String(50), nullable=False)
    confidence = db.Column(db.Float, nullable=False)
    symptoms = db.Column(db.Text, nullable=False)  # JSON string
    exposure_locations = db.Column(db.Text)  # JSON string
    location_warning = db.Column(db.Text)  # JSON string
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'prediction': self.prediction,
            'confidence': self.confidence,
            'symptoms': json.loads(self.symptoms) if self.symptoms else {},
            'exposure_locations': json.loads(self.exposure_locations) if self.exposure_locations else None,
            'location_warning': json.loads(self.location_warning) if self.location_warning else None,
            'created_at': self.created_at.isoformat(),
            'username': self.user.username,
            'full_name': self.user.full_name,
            'email': self.user.email
        }

# Utility functions
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    return hash_password(password) == hashed

# Initialize database
def create_tables():
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin_user = User(
                username='admin',
                password=hash_password('admin123'),
                email='<EMAIL>',
                full_name='System Administrator',
                is_admin=True
            )
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: admin/admin123")

# API Routes

@app.route('/api/health', methods=['GET'])
def health_check():
    try:
        # Test database connection
        db.session.execute(db.text('SELECT 1'))
        db_status = 'connected'
        db_type = 'PostgreSQL' if 'postgresql' in app.config['SQLALCHEMY_DATABASE_URI'] else 'SQLite'
    except Exception as e:
        db_status = f'error: {str(e)}'
        db_type = 'unknown'

    return jsonify({
        'status': 'healthy',
        'message': 'HIV Predictor API is running',
        'database': db_status,
        'database_type': db_type,
        'model_loaded': True
    })

@app.route('/api/auth/signup', methods=['POST'])
def signup():
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['username', 'password', 'email', 'full_name']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if username already exists
        existing_user = User.query.filter_by(username=data['username']).first()
        if existing_user:
            return jsonify({'error': 'Username already exists'}), 400
        
        # Validate password length
        if len(data['password']) < 6:
            return jsonify({'error': 'Password must be at least 6 characters long'}), 400
        
        # Create new user
        new_user = User(
            username=data['username'].lower().strip(),
            password=hash_password(data['password']),
            email=data['email'].lower().strip(),
            full_name=data['full_name'].strip(),
            is_admin=False
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Account created successfully',
            'user': new_user.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/signin', methods=['POST'])
def signin():
    try:
        data = request.get_json()
        
        if not data.get('username') or not data.get('password'):
            return jsonify({'error': 'Username and password are required'}), 400
        
        # Find user
        user = User.query.filter_by(username=data['username'].lower().strip()).first()
        
        if not user:
            return jsonify({'error': 'Invalid username or password'}), 401
        
        # Verify password (support both hashed and plain for admin)
        if verify_password(data['password'], user.password) or (user.username == 'admin' and data['password'] == 'admin123'):
            return jsonify({
                'success': True,
                'message': f'Welcome back, {user.full_name}!',
                'user': user.to_dict()
            }), 200
        else:
            return jsonify({'error': 'Invalid username or password'}), 401
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users', methods=['GET'])
def get_all_users():
    try:
        users = User.query.order_by(User.created_at.desc()).all()
        return jsonify({
            'success': True,
            'users': [user.to_dict() for user in users]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/predictions', methods=['POST'])
def save_prediction():
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['user_id', 'prediction', 'confidence', 'symptoms']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Verify user exists
        user = User.query.get(data['user_id'])
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Create prediction result
        prediction_result = PredictionResult(
            user_id=data['user_id'],
            prediction=data['prediction'],
            confidence=data['confidence'],
            symptoms=json.dumps(data['symptoms']),
            exposure_locations=json.dumps(data.get('exposure_locations')) if data.get('exposure_locations') else None,
            location_warning=json.dumps(data.get('location_warning')) if data.get('location_warning') else None
        )
        
        db.session.add(prediction_result)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Prediction saved successfully',
            'prediction': prediction_result.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/predictions/user/<int:user_id>', methods=['GET'])
def get_user_predictions(user_id):
    try:
        predictions = PredictionResult.query.filter_by(user_id=user_id).order_by(PredictionResult.created_at.desc()).all()
        return jsonify({
            'success': True,
            'predictions': [pred.to_dict() for pred in predictions]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/predictions/all', methods=['GET'])
def get_all_predictions():
    try:
        predictions = PredictionResult.query.order_by(PredictionResult.created_at.desc()).all()
        return jsonify({
            'success': True,
            'predictions': [pred.to_dict() for pred in predictions]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats', methods=['GET'])
def get_statistics():
    try:
        total_users = User.query.count()
        total_predictions = PredictionResult.query.count()
        high_risk = PredictionResult.query.filter(PredictionResult.prediction.like('%high%')).count()
        medium_risk = PredictionResult.query.filter(PredictionResult.prediction.like('%medium%')).count()
        low_risk = PredictionResult.query.filter(PredictionResult.prediction.like('%low%')).count()
        
        # Today's predictions
        today = datetime.now(timezone.utc).date()
        today_predictions = PredictionResult.query.filter(
            db.func.date(PredictionResult.created_at) == today
        ).count()
        
        return jsonify({
            'success': True,
            'stats': {
                'total_users': total_users,
                'total_predictions': total_predictions,
                'high_risk_predictions': high_risk,
                'medium_risk_predictions': medium_risk,
                'low_risk_predictions': low_risk,
                'today_predictions': today_predictions
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ML Prediction Logic (from original API)
def simple_prediction(symptoms):
    """
    Enhanced rule-based HIV risk assessment with Yes/No/Sometimes options
    Symptom values: 0=No, 1=Sometimes, 2=Yes
    Returns risk level based on symptom severity and risk factors
    """
    # Calculate weighted symptom score (0=No, 1=Sometimes, 2=Yes)
    # Original physical symptoms
    symptom_keys = ['fever', 'headache', 'skin_rash', 'muscle_pain', 'weight_loss',
                   'fatigue', 'oral_ulcers', 'swollen_lymph_nodes', 'diarrhea', 'night_sweats',
                   # New early/acute HIV symptoms
                   'sore_throat', 'joint_pain', 'nausea', 'loss_of_appetite', 'chills', 'persistent_cough',
                   # New advanced/chronic HIV symptoms
                   'recurring_infections', 'memory_problems', 'vision_problems', 'persistent_headaches']

    symptom_score = sum(symptoms.get(key, 0) for key in symptom_keys)
    max_symptom_score = len(symptom_keys) * 2  # Maximum possible score

    # Calculate risk behavior score
    risk_behavior_keys = ['unprotected_sex', 'shared_needles', 'multiple_partners',
                         'blood_transfusion', 'tattoo_piercing_unsterile', 'partner_hiv_positive']
    risk_behavior_score = sum(symptoms.get(key, 0) for key in risk_behavior_keys)
    max_risk_score = len(risk_behavior_keys) * 2  # Maximum possible score

    # Normalize scores (0-1 range)
    normalized_symptoms = symptom_score / max_symptom_score if max_symptom_score > 0 else 0
    normalized_risks = risk_behavior_score / max_risk_score if max_risk_score > 0 else 0

    # Calculate overall risk score (risk behaviors weighted more heavily)
    overall_risk = (normalized_symptoms * 0.3) + (normalized_risks * 0.7)

    # Determine risk level and confidence
    if overall_risk >= 0.6:
        return "High Risk", min(0.95, 0.6 + (overall_risk * 0.3))
    elif overall_risk >= 0.3:
        return "Medium Risk", min(0.8, 0.4 + (overall_risk * 0.4))
    else:
        return "Low Risk", max(0.1, overall_risk * 0.5)

@app.route('/api/predict', methods=['POST'])
def predict():
    try:
        # Get data from request
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Handle both old format (direct symptoms) and new format (with locations)
        if 'symptoms' in data:
            # New format with locations
            symptoms = data['symptoms']
            exposure_locations = data.get('exposure_locations', {})
            user_id = data.get('user_id')
        else:
            # Old format (direct symptoms)
            symptoms = data
            exposure_locations = {}
            user_id = None

        # Expected symptom order - Enhanced with additional symptoms
        symptom_order = [
            # Original physical symptoms
            'fever', 'headache', 'skin_rash', 'muscle_pain', 'weight_loss',
            'fatigue', 'oral_ulcers', 'swollen_lymph_nodes', 'diarrhea', 'night_sweats',
            # New early/acute HIV symptoms
            'sore_throat', 'joint_pain', 'nausea', 'loss_of_appetite', 'chills', 'persistent_cough',
            # New advanced/chronic HIV symptoms
            'recurring_infections', 'memory_problems', 'vision_problems', 'persistent_headaches',
            # Original risk behaviors
            'unprotected_sex', 'shared_needles', 'multiple_partners',
            # New risk factors
            'blood_transfusion', 'tattoo_piercing_unsterile', 'partner_hiv_positive'
        ]

        # Validate all required symptoms are present
        missing_symptoms = [symptom for symptom in symptom_order if symptom not in symptoms]
        if missing_symptoms:
            return jsonify({
                'error': f'Missing symptoms: {", ".join(missing_symptoms)}'
            }), 400

        # Use enhanced rule-based prediction
        prediction, confidence = simple_prediction(symptoms)

        # Prepare response
        response_data = {
            'prediction': prediction,
            'confidence': float(confidence),
            'features_used': symptom_order
        }

        # Add exposure locations if provided
        if exposure_locations:
            response_data['exposure_locations'] = exposure_locations

            # Add location-based risk assessment
            high_risk_locations = []
            for behavior, location in exposure_locations.items():
                if location and behavior in ['unprotected_sex', 'shared_needles', 'multiple_partners']:
                    high_risk_locations.append(f"{behavior.replace('_', ' ')}: {location}")

            if high_risk_locations:
                response_data['location_warning'] = {
                    'message': 'High-risk exposure locations reported',
                    'locations': high_risk_locations,
                    'recommendation': 'Please consider getting tested and informing healthcare providers about these locations for contact tracing.'
                }

        # Auto-save prediction if user_id is provided
        if user_id:
            try:
                prediction_result = PredictionResult(
                    user_id=user_id,
                    prediction=prediction,
                    confidence=confidence,
                    symptoms=json.dumps(symptoms),
                    exposure_locations=json.dumps(exposure_locations) if exposure_locations else None,
                    location_warning=json.dumps(response_data.get('location_warning')) if response_data.get('location_warning') else None
                )

                db.session.add(prediction_result)
                db.session.commit()
                response_data['saved'] = True
            except Exception as e:
                print(f"Error saving prediction: {e}")
                response_data['saved'] = False

        return jsonify(response_data)

    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/healthcare/providers', methods=['GET'])
def get_healthcare_providers():
    try:
        # Sample healthcare providers (in production, this would come from database)
        providers = [
            {
                'id': 1,
                'name': 'City General Hospital',
                'type': 'hospital',
                'address': '123 Main Street, Downtown',
                'phone': '******-0101',
                'email': '<EMAIL>',
                'services': ['Emergency Care', 'HIV Testing', 'HIV Treatment', 'General Medicine'],
                'latitude': 40.7128,
                'longitude': -74.0060,
                'rating': 4.5,
                'opening_hours': '24/7',
                'is_emergency': True,
                'has_hiv_testing': True,
                'has_hiv_treatment': True,
                'is_verified': True,
            },
            {
                'id': 2,
                'name': 'Community Health Clinic',
                'type': 'clinic',
                'address': '456 Oak Avenue, Midtown',
                'phone': '******-0102',
                'email': '<EMAIL>',
                'services': ['HIV Testing', 'STD Testing', 'Counseling', 'Preventive Care'],
                'latitude': 40.7589,
                'longitude': -73.9851,
                'rating': 4.2,
                'opening_hours': 'Mon-Fri 8AM-6PM, Sat 9AM-3PM',
                'is_emergency': False,
                'has_hiv_testing': True,
                'has_hiv_treatment': False,
                'is_verified': True,
            },
            {
                'id': 3,
                'name': 'Rapid Testing Center',
                'type': 'testing_center',
                'address': '789 Pine Street, Uptown',
                'phone': '******-0103',
                'email': '<EMAIL>',
                'services': ['HIV Testing', 'STD Testing', 'Rapid Results'],
                'latitude': 40.7831,
                'longitude': -73.9712,
                'rating': 4.8,
                'opening_hours': 'Mon-Sun 7AM-10PM',
                'is_emergency': False,
                'has_hiv_testing': True,
                'has_hiv_treatment': False,
                'is_verified': True,
            },
            {
                'id': 4,
                'name': 'Metro Medical Center',
                'type': 'hospital',
                'address': '321 Elm Street, Westside',
                'phone': '******-0104',
                'email': '<EMAIL>',
                'services': ['Emergency Care', 'HIV Treatment', 'Infectious Disease', 'Pharmacy'],
                'latitude': 40.7505,
                'longitude': -74.0134,
                'rating': 4.3,
                'opening_hours': '24/7',
                'is_emergency': True,
                'has_hiv_testing': True,
                'has_hiv_treatment': True,
                'is_verified': True,
            },
            {
                'id': 5,
                'name': 'Wellness Pharmacy',
                'type': 'pharmacy',
                'address': '654 Maple Drive, Eastside',
                'phone': '******-0105',
                'email': '<EMAIL>',
                'services': ['Prescription Medications', 'HIV Medications', 'Consultation'],
                'latitude': 40.7282,
                'longitude': -73.9942,
                'rating': 4.0,
                'opening_hours': 'Mon-Sat 8AM-9PM, Sun 10AM-6PM',
                'is_emergency': False,
                'has_hiv_testing': False,
                'has_hiv_treatment': True,
                'is_verified': True,
            },
        ]

        # Filter by query parameters
        provider_type = request.args.get('type')
        hiv_testing = request.args.get('hiv_testing') == 'true'
        hiv_treatment = request.args.get('hiv_treatment') == 'true'
        emergency = request.args.get('emergency') == 'true'

        filtered_providers = providers

        if provider_type:
            filtered_providers = [p for p in filtered_providers if p['type'] == provider_type]
        if hiv_testing:
            filtered_providers = [p for p in filtered_providers if p['has_hiv_testing']]
        if hiv_treatment:
            filtered_providers = [p for p in filtered_providers if p['has_hiv_treatment']]
        if emergency:
            filtered_providers = [p for p in filtered_providers if p['is_emergency']]

        return jsonify({
            'success': True,
            'providers': filtered_providers
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Initialize database tables
    create_tables()
    app.run(debug=True, host='0.0.0.0', port=5001)


