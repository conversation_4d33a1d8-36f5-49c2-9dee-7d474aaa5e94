#!/usr/bin/env python3
"""
Database Initialization Script for HIV Predictor App
This script initializes the PostgreSQL database with tables and default data
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

try:
    from app import app, db, User, PredictionResult, hash_password
except ImportError as e:
    print(f"❌ Error importing app modules: {e}")
    print("Make sure you're running this script from the flask_backend directory")
    sys.exit(1)

def test_connection():
    """Test database connection"""
    try:
        with app.app_context():
            # Test connection
            db.session.execute(db.text('SELECT 1'))
            print("✅ Database connection successful")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("\n💡 Common issues:")
        print("- Check your .env file configuration")
        print("- Ensure PostgreSQL server is running")
        print("- Verify connection string is correct")
        return False

def create_tables():
    """Create database tables"""
    try:
        with app.app_context():
            print("🔧 Creating database tables...")
            db.create_all()
            print("✅ Database tables created successfully")
            return True
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def create_admin_user():
    """Create default admin user"""
    try:
        with app.app_context():
            # Check if admin user already exists
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("ℹ️  Admin user already exists")
                return True
            
            print("👤 Creating default admin user...")
            admin_user = User(
                username='admin',
                password=hash_password('admin123'),
                email='<EMAIL>',
                full_name='System Administrator',
                is_admin=True
            )
            
            db.session.add(admin_user)
            db.session.commit()
            print("✅ Admin user created successfully")
            print("   Username: admin")
            print("   Password: admin123")
            return True
            
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        db.session.rollback()
        return False

def show_database_info():
    """Show database information"""
    try:
        with app.app_context():
            # Get database info
            db_url = app.config['SQLALCHEMY_DATABASE_URI']
            
            # Count existing data
            user_count = User.query.count()
            prediction_count = PredictionResult.query.count()
            
            print("\n📊 Database Information:")
            print(f"   Database Type: PostgreSQL")
            print(f"   Users: {user_count}")
            print(f"   Predictions: {prediction_count}")
            
            if user_count > 0:
                print("\n👥 Existing Users:")
                users = User.query.all()
                for user in users:
                    admin_status = " (Admin)" if user.is_admin else ""
                    print(f"   - {user.username}{admin_status}")
            
    except Exception as e:
        print(f"⚠️  Could not retrieve database info: {e}")

def main():
    """Main initialization function"""
    print("🏥 HIV Predictor App - Database Initialization")
    print("=" * 55)
    
    # Check if .env file exists
    env_path = '.env'
    if not os.path.exists(env_path):
        print(f"❌ {env_path} file not found!")
        print("Please run setup_postgresql.py first to configure your database.")
        sys.exit(1)
    
    print("🔍 Testing database connection...")
    if not test_connection():
        sys.exit(1)
    
    print("\n🔧 Initializing database...")
    if not create_tables():
        sys.exit(1)
    
    print("\n👤 Setting up admin user...")
    if not create_admin_user():
        sys.exit(1)
    
    print("\n📊 Database initialization complete!")
    show_database_info()
    
    print("\n🚀 Next steps:")
    print("1. Start the Flask server: python app.py")
    print("2. Test the API: curl http://localhost:5001/api/health")
    print("3. Login as admin with username 'admin' and password 'admin123'")
    
    print("\n💡 Tips:")
    print("- Your data is now stored in PostgreSQL")
    print("- All predictions will be automatically saved")
    print("- Admin dashboard will show all user data")

if __name__ == '__main__':
    main()
