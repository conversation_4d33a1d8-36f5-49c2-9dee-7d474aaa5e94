// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCMarbwOuMpHtNHCUBGMpREXpsNCYk-ARA',
    appId: '1:494872782956:web:257e5d16a5ba7b0869d049',
    messagingSenderId: '494872782956',
    projectId: 'hiv-predictor-app',
    authDomain: 'hiv-predictor-app.firebaseapp.com',
    storageBucket: 'hiv-predictor-app.firebasestorage.app',
    measurementId: 'G-9SK2PMSYWX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC28UebgvuTDjBHcFMvu9I7YAqsgfGaaRI',
    appId: '1:494872782956:android:10a5faebd87de68d69d049',
    messagingSenderId: '494872782956',
    projectId: 'hiv-predictor-app',
    storageBucket: 'hiv-predictor-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyClaO0RvvTed3EcWBCZtjRf7lYHQdtkSGA',
    appId: '1:494872782956:ios:424c33678e78699269d049',
    messagingSenderId: '494872782956',
    projectId: 'hiv-predictor-app',
    storageBucket: 'hiv-predictor-app.firebasestorage.app',
    iosBundleId: 'com.example.hivPredictorApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyClaO0RvvTed3EcWBCZtjRf7lYHQdtkSGA',
    appId: '1:494872782956:ios:424c33678e78699269d049',
    messagingSenderId: '494872782956',
    projectId: 'hiv-predictor-app',
    storageBucket: 'hiv-predictor-app.firebasestorage.app',
    iosBundleId: 'com.example.hivPredictorApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCMarbwOuMpHtNHCUBGMpREXpsNCYk-ARA',
    appId: '1:494872782956:web:d72489d0dbe3b28569d049',
    messagingSenderId: '494872782956',
    projectId: 'hiv-predictor-app',
    authDomain: 'hiv-predictor-app.firebaseapp.com',
    storageBucket: 'hiv-predictor-app.firebasestorage.app',
    measurementId: 'G-THRXPMDKST',
  );
}
