class AppSettings {
  final bool notificationsEnabled;
  final bool riskAlertsEnabled;
  final bool hospitalNotificationsEnabled;
  final bool followUpRemindersEnabled;
  final bool locationTrackingEnabled;
  final bool dataAnalyticsEnabled;
  final bool biometricAuthEnabled;
  final String theme; // light, dark, system
  final String language; // en, es, fr, etc.
  final int reminderFrequency; // in hours
  final bool autoBackupEnabled;
  final bool shareAnonymousData;
  final bool emergencyContactsEnabled;
  final String emergencyContact;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final double textSize; // 1.0 = normal, 1.2 = large, etc.
  final bool highContrastMode;
  final bool offlineModeEnabled;
  final int dataRetentionDays;
  final bool exportDataEnabled;

  AppSettings({
    this.notificationsEnabled = true,
    this.riskAlertsEnabled = true,
    this.hospitalNotificationsEnabled = true,
    this.followUpRemindersEnabled = true,
    this.locationTrackingEnabled = true,
    this.dataAnalyticsEnabled = true,
    this.biometricAuthEnabled = false,
    this.theme = 'system',
    this.language = 'en',
    this.reminderFrequency = 24,
    this.autoBackupEnabled = true,
    this.shareAnonymousData = false,
    this.emergencyContactsEnabled = false,
    this.emergencyContact = '',
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.textSize = 1.0,
    this.highContrastMode = false,
    this.offlineModeEnabled = false,
    this.dataRetentionDays = 365,
    this.exportDataEnabled = true,
  });

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      notificationsEnabled: json['notifications_enabled'] ?? true,
      riskAlertsEnabled: json['risk_alerts_enabled'] ?? true,
      hospitalNotificationsEnabled: json['hospital_notifications_enabled'] ?? true,
      followUpRemindersEnabled: json['follow_up_reminders_enabled'] ?? true,
      locationTrackingEnabled: json['location_tracking_enabled'] ?? true,
      dataAnalyticsEnabled: json['data_analytics_enabled'] ?? true,
      biometricAuthEnabled: json['biometric_auth_enabled'] ?? false,
      theme: json['theme'] ?? 'system',
      language: json['language'] ?? 'en',
      reminderFrequency: json['reminder_frequency'] ?? 24,
      autoBackupEnabled: json['auto_backup_enabled'] ?? true,
      shareAnonymousData: json['share_anonymous_data'] ?? false,
      emergencyContactsEnabled: json['emergency_contacts_enabled'] ?? false,
      emergencyContact: json['emergency_contact'] ?? '',
      soundEnabled: json['sound_enabled'] ?? true,
      vibrationEnabled: json['vibration_enabled'] ?? true,
      textSize: (json['text_size'] ?? 1.0).toDouble(),
      highContrastMode: json['high_contrast_mode'] ?? false,
      offlineModeEnabled: json['offline_mode_enabled'] ?? false,
      dataRetentionDays: json['data_retention_days'] ?? 365,
      exportDataEnabled: json['export_data_enabled'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notifications_enabled': notificationsEnabled,
      'risk_alerts_enabled': riskAlertsEnabled,
      'hospital_notifications_enabled': hospitalNotificationsEnabled,
      'follow_up_reminders_enabled': followUpRemindersEnabled,
      'location_tracking_enabled': locationTrackingEnabled,
      'data_analytics_enabled': dataAnalyticsEnabled,
      'biometric_auth_enabled': biometricAuthEnabled,
      'theme': theme,
      'language': language,
      'reminder_frequency': reminderFrequency,
      'auto_backup_enabled': autoBackupEnabled,
      'share_anonymous_data': shareAnonymousData,
      'emergency_contacts_enabled': emergencyContactsEnabled,
      'emergency_contact': emergencyContact,
      'sound_enabled': soundEnabled,
      'vibration_enabled': vibrationEnabled,
      'text_size': textSize,
      'high_contrast_mode': highContrastMode,
      'offline_mode_enabled': offlineModeEnabled,
      'data_retention_days': dataRetentionDays,
      'export_data_enabled': exportDataEnabled,
    };
  }

  AppSettings copyWith({
    bool? notificationsEnabled,
    bool? riskAlertsEnabled,
    bool? hospitalNotificationsEnabled,
    bool? followUpRemindersEnabled,
    bool? locationTrackingEnabled,
    bool? dataAnalyticsEnabled,
    bool? biometricAuthEnabled,
    String? theme,
    String? language,
    int? reminderFrequency,
    bool? autoBackupEnabled,
    bool? shareAnonymousData,
    bool? emergencyContactsEnabled,
    String? emergencyContact,
    bool? soundEnabled,
    bool? vibrationEnabled,
    double? textSize,
    bool? highContrastMode,
    bool? offlineModeEnabled,
    int? dataRetentionDays,
    bool? exportDataEnabled,
  }) {
    return AppSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      riskAlertsEnabled: riskAlertsEnabled ?? this.riskAlertsEnabled,
      hospitalNotificationsEnabled: hospitalNotificationsEnabled ?? this.hospitalNotificationsEnabled,
      followUpRemindersEnabled: followUpRemindersEnabled ?? this.followUpRemindersEnabled,
      locationTrackingEnabled: locationTrackingEnabled ?? this.locationTrackingEnabled,
      dataAnalyticsEnabled: dataAnalyticsEnabled ?? this.dataAnalyticsEnabled,
      biometricAuthEnabled: biometricAuthEnabled ?? this.biometricAuthEnabled,
      theme: theme ?? this.theme,
      language: language ?? this.language,
      reminderFrequency: reminderFrequency ?? this.reminderFrequency,
      autoBackupEnabled: autoBackupEnabled ?? this.autoBackupEnabled,
      shareAnonymousData: shareAnonymousData ?? this.shareAnonymousData,
      emergencyContactsEnabled: emergencyContactsEnabled ?? this.emergencyContactsEnabled,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      textSize: textSize ?? this.textSize,
      highContrastMode: highContrastMode ?? this.highContrastMode,
      offlineModeEnabled: offlineModeEnabled ?? this.offlineModeEnabled,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      exportDataEnabled: exportDataEnabled ?? this.exportDataEnabled,
    );
  }
}
