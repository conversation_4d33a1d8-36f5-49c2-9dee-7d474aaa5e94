class HealthcareProvider {
  final int id;
  final String name;
  final String type; // hospital, clinic, testing_center, pharmacy
  final String address;
  final String phone;
  final String email;
  final List<String> services;
  final double latitude;
  final double longitude;
  final double rating;
  final String openingHours;
  final bool isEmergency;
  final bool hasHivTesting;
  final bool hasHivTreatment;
  final bool isVerified;
  final double distanceKm;

  HealthcareProvider({
    required this.id,
    required this.name,
    required this.type,
    required this.address,
    required this.phone,
    this.email = '',
    required this.services,
    required this.latitude,
    required this.longitude,
    this.rating = 0.0,
    this.openingHours = '24/7',
    this.isEmergency = false,
    this.hasHivTesting = false,
    this.hasHivTreatment = false,
    this.isVerified = true,
    this.distanceKm = 0.0,
  });

  factory HealthcareProvider.fromJson(Map<String, dynamic> json) {
    return HealthcareProvider(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      type: json['type'] ?? 'clinic',
      address: json['address'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      services: List<String>.from(json['services'] ?? []),
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      rating: (json['rating'] ?? 0.0).toDouble(),
      openingHours: json['opening_hours'] ?? '24/7',
      isEmergency: json['is_emergency'] ?? false,
      hasHivTesting: json['has_hiv_testing'] ?? false,
      hasHivTreatment: json['has_hiv_treatment'] ?? false,
      isVerified: json['is_verified'] ?? true,
      distanceKm: (json['distance_km'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'address': address,
      'phone': phone,
      'email': email,
      'services': services,
      'latitude': latitude,
      'longitude': longitude,
      'rating': rating,
      'opening_hours': openingHours,
      'is_emergency': isEmergency,
      'has_hiv_testing': hasHivTesting,
      'has_hiv_treatment': hasHivTreatment,
      'is_verified': isVerified,
      'distance_km': distanceKm,
    };
  }

  String get typeIcon {
    switch (type.toLowerCase()) {
      case 'hospital':
        return '🏥';
      case 'clinic':
        return '🏥';
      case 'testing_center':
        return '🧪';
      case 'pharmacy':
        return '💊';
      default:
        return '🏥';
    }
  }

  String get typeDisplayName {
    switch (type.toLowerCase()) {
      case 'hospital':
        return 'Hospital';
      case 'clinic':
        return 'Clinic';
      case 'testing_center':
        return 'Testing Center';
      case 'pharmacy':
        return 'Pharmacy';
      default:
        return 'Healthcare Facility';
    }
  }

  String get distanceText {
    if (distanceKm < 1) {
      return '${(distanceKm * 1000).round()}m away';
    } else {
      return '${distanceKm.toStringAsFixed(1)}km away';
    }
  }

  String get ratingText {
    if (rating > 0) {
      return '⭐ ${rating.toStringAsFixed(1)}';
    }
    return 'No rating';
  }

  bool get isOpen24Hours {
    return openingHours.toLowerCase().contains('24') || 
           openingHours.toLowerCase().contains('24/7');
  }

  List<String> get availableServices {
    List<String> allServices = [...services];
    
    if (hasHivTesting) {
      allServices.add('HIV Testing');
    }
    if (hasHivTreatment) {
      allServices.add('HIV Treatment');
    }
    if (isEmergency) {
      allServices.add('Emergency Care');
    }
    
    return allServices.toSet().toList(); // Remove duplicates
  }
}
