import 'healthcare_provider.dart';

class SmartNotification {
  final int id;
  final int userId;
  final String title;
  final String message;
  final String riskLevel; // high, medium, low
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime createdAt;
  final DateTime? scheduledFor;
  final bool isRead;
  final bool isActionable;
  final Map<String, dynamic>? actionData;
  final List<HealthcareProvider>? nearbyProviders;

  SmartNotification({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.riskLevel,
    required this.type,
    required this.priority,
    required this.createdAt,
    this.scheduledFor,
    this.isRead = false,
    this.isActionable = false,
    this.actionData,
    this.nearbyProviders,
  });

  factory SmartNotification.fromJson(Map<String, dynamic> json) {
    return SmartNotification(
      id: json['id'],
      userId: json['user_id'],
      title: json['title'],
      message: json['message'],
      riskLevel: json['risk_level'],
      type: NotificationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => NotificationType.general,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.toString().split('.').last == json['priority'],
        orElse: () => NotificationPriority.medium,
      ),
      createdAt: DateTime.parse(json['created_at']),
      scheduledFor: json['scheduled_for'] != null 
          ? DateTime.parse(json['scheduled_for']) 
          : null,
      isRead: json['is_read'] ?? false,
      isActionable: json['is_actionable'] ?? false,
      actionData: json['action_data'],
      nearbyProviders: json['nearby_providers'] != null
          ? (json['nearby_providers'] as List)
              .map((p) => HealthcareProvider.fromJson(p))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'message': message,
      'risk_level': riskLevel,
      'type': type.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'scheduled_for': scheduledFor?.toIso8601String(),
      'is_read': isRead,
      'is_actionable': isActionable,
      'action_data': actionData,
      'nearby_providers': nearbyProviders?.map((p) => p.toJson()).toList(),
    };
  }

  String get priorityIcon {
    switch (priority) {
      case NotificationPriority.urgent:
        return '🚨';
      case NotificationPriority.high:
        return '⚠️';
      case NotificationPriority.medium:
        return '📢';
      case NotificationPriority.low:
        return 'ℹ️';
    }
  }

  String get riskIcon {
    switch (riskLevel.toLowerCase()) {
      case 'high':
        return '🔴';
      case 'medium':
        return '🟠';
      case 'low':
        return '🟢';
      default:
        return '⚪';
    }
  }

  String get typeIcon {
    switch (type) {
      case NotificationType.riskAlert:
        return '⚠️';
      case NotificationType.hospitalDirection:
        return '🏥';
      case NotificationType.testingReminder:
        return '🧪';
      case NotificationType.educational:
        return '📚';
      case NotificationType.support:
        return '🤝';
      case NotificationType.emergency:
        return '🚨';
      case NotificationType.general:
        return '📢';
    }
  }

  bool get isUrgent {
    return priority == NotificationPriority.urgent || 
           type == NotificationType.emergency;
  }

  bool get requiresImmediateAction {
    return isUrgent && isActionable;
  }
}

enum NotificationType {
  riskAlert,
  hospitalDirection,
  testingReminder,
  educational,
  support,
  emergency,
  general,
}

enum NotificationPriority {
  urgent,
  high,
  medium,
  low,
}

class NotificationAction {
  final String label;
  final String action;
  final Map<String, dynamic>? data;

  NotificationAction({
    required this.label,
    required this.action,
    this.data,
  });

  factory NotificationAction.findHospitals() {
    return NotificationAction(
      label: 'Find Nearest Hospital',
      action: 'find_hospitals',
    );
  }

  factory NotificationAction.callEmergency() {
    return NotificationAction(
      label: 'Call Emergency',
      action: 'call_emergency',
      data: {'phone': '911'},
    );
  }

  factory NotificationAction.scheduleTest() {
    return NotificationAction(
      label: 'Schedule HIV Test',
      action: 'schedule_test',
    );
  }

  factory NotificationAction.learnMore() {
    return NotificationAction(
      label: 'Learn More',
      action: 'learn_more',
    );
  }
}
