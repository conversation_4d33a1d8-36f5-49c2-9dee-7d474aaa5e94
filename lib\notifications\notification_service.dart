import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest_all.dart' as tz;
import '../models/healthcare_provider.dart';
import '../services/healthcare_provider_service.dart';
import 'package:permission_handler/permission_handler.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    tz.initializeTimeZones();

    // Request notification permission
    final status = await Permission.notification.request();
    if (status.isDenied) {
      debugPrint('Notification permission denied');
      return;
    }

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        debugPrint('Notification clicked: ${response.payload}');
      },
    );

    // Create notification channels
    await _createNotificationChannels();
  }

  static Future<void> _createNotificationChannels() async {
    const AndroidNotificationChannel hivPredictorChannel =
        AndroidNotificationChannel(
          'hiv_predictor_channel',
          'HIV Predictor Notifications',
          description: 'Notification channel for the HIV Predictor app',
          importance: Importance.max,
        );

    const AndroidNotificationChannel dailyChannel = AndroidNotificationChannel(
      'daily_channel',
      'Daily Reminders',
      description: 'Daily reminder notifications',
      importance: Importance.max,
    );

    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(hivPredictorChannel);

    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(dailyChannel);
  }

  static Future<void> showNotification({
    required String title,
    required String body,
  }) async {
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
          'hiv_predictor_channel',
          'HIV Predictor Notifications',
          channelDescription: 'Notification channel for the HIV Predictor app',
          importance: Importance.max,
          priority: Priority.high,
          showWhen: true,
        );

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
    );

    await _notificationsPlugin.show(0, title, body, notificationDetails);
  }

  static Future<void> scheduleDailyNotification({
    required String title,
    required String body,
    required TimeOfDay time,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'daily_channel',
      'Daily Reminders',
      channelDescription: 'Daily reminder notifications',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    final now = DateTime.now();
    final firstNotificationTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    final scheduleTime = tz.TZDateTime.from(
      firstNotificationTime.isBefore(now)
          ? firstNotificationTime.add(const Duration(days: 1))
          : firstNotificationTime,
      tz.local,
    );

    await _notificationsPlugin.zonedSchedule(
      0,
      title,
      body,
      scheduleTime,
      notificationDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  // Enhanced Smart Notification Methods
  static Future<void> showSmartRiskNotification({
    required String riskLevel,
    required double confidence,
    Map<String, String>? exposureLocations,
    int? userId,
  }) async {
    String title;
    String body;
    String channelId;
    Priority priority;

    switch (riskLevel.toLowerCase()) {
      case 'high risk':
        title = '🚨 URGENT: High HIV Risk Detected';
        body =
            'Your assessment indicates HIGH RISK. Please visit the nearest hospital or testing center IMMEDIATELY for HIV testing and consultation.';
        channelId = 'high_risk_channel';
        priority = Priority.max;
        break;
      case 'medium risk':
        title = '⚠️ Medium HIV Risk - Action Needed';
        body =
            'Your assessment indicates MEDIUM RISK. We recommend visiting a nearby clinic for HIV testing within the next few days.';
        channelId = 'medium_risk_channel';
        priority = Priority.high;
        break;
      case 'low risk':
        title = '✅ Low HIV Risk - Stay Aware';
        body =
            'Your assessment indicates LOW RISK. Continue practicing safe behaviors and consider regular testing for peace of mind.';
        channelId = 'low_risk_channel';
        priority = Priority.defaultPriority;
        break;
      default:
        title = 'HIV Risk Assessment Complete';
        body =
            'Your risk assessment has been completed. Please review the results.';
        channelId = 'general_channel';
        priority = Priority.defaultPriority;
    }

    // Add exposure location warning if available
    if (exposureLocations != null && exposureLocations.isNotEmpty) {
      body +=
          '\n\n📍 High-risk exposure locations reported. Please inform healthcare providers for contact tracing.';
    }

    // Add action buttons based on risk level
    String actionText = '';
    switch (riskLevel.toLowerCase()) {
      case 'high risk':
        actionText = '\n\n🏥 Tap to find nearest hospital';
        break;
      case 'medium risk':
        actionText = '\n\n🏥 Tap to find nearby testing centers';
        break;
      case 'low risk':
        actionText = '\n\n📚 Tap to learn more about prevention';
        break;
    }
    body += actionText;

    await _showSmartNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: title,
      body: body,
      channelId: channelId,
      priority: priority,
      riskLevel: riskLevel,
    );

    // Show follow-up notification for medium and high risk
    if (riskLevel.toLowerCase() == 'high risk' ||
        riskLevel.toLowerCase() == 'medium risk') {
      await _scheduleFollowUpNotification(riskLevel, userId);
    }
  }

  static Future<void> _showSmartNotification({
    required int id,
    required String title,
    required String body,
    required String channelId,
    required Priority priority,
    required String riskLevel,
  }) async {
    final AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
          channelId,
          _getChannelName(channelId),
          channelDescription: _getChannelDescription(channelId),
          importance: _getImportance(priority),
          priority: priority,
          showWhen: true,
          icon: 'app_icon',
          color: _getRiskColor(riskLevel),
          enableVibration: true,
          playSound: true,
          actions: _getNotificationActions(riskLevel),
        );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notificationsPlugin.show(id, title, body, notificationDetails);
  }

  static String _getChannelName(String channelId) {
    switch (channelId) {
      case 'high_risk_channel':
        return 'High Risk Alerts';
      case 'medium_risk_channel':
        return 'Medium Risk Alerts';
      case 'low_risk_channel':
        return 'Low Risk Notifications';
      default:
        return 'General Notifications';
    }
  }

  static String _getChannelDescription(String channelId) {
    switch (channelId) {
      case 'high_risk_channel':
        return 'Urgent notifications for high HIV risk assessments';
      case 'medium_risk_channel':
        return 'Important notifications for medium HIV risk assessments';
      case 'low_risk_channel':
        return 'Informational notifications for low HIV risk assessments';
      default:
        return 'General app notifications';
    }
  }

  static Importance _getImportance(Priority priority) {
    switch (priority) {
      case Priority.max:
        return Importance.max;
      case Priority.high:
        return Importance.high;
      case Priority.defaultPriority:
        return Importance.defaultImportance;
      case Priority.low:
        return Importance.low;
      case Priority.min:
        return Importance.min;
    }
  }

  static Color? _getRiskColor(String riskLevel) {
    switch (riskLevel.toLowerCase()) {
      case 'high risk':
        return const Color(0xFFD32F2F); // Red
      case 'medium risk':
        return const Color(0xFFFF9800); // Orange
      case 'low risk':
        return const Color(0xFF4CAF50); // Green
      default:
        return null;
    }
  }

  static List<AndroidNotificationAction>? _getNotificationActions(
    String riskLevel,
  ) {
    switch (riskLevel.toLowerCase()) {
      case 'high risk':
        return [
          const AndroidNotificationAction(
            'find_hospital',
            'Find Hospital',
            icon: DrawableResourceAndroidBitmap('ic_hospital'),
          ),
          const AndroidNotificationAction(
            'call_emergency',
            'Call Emergency',
            icon: DrawableResourceAndroidBitmap('ic_phone'),
          ),
        ];
      case 'medium risk':
        return [
          const AndroidNotificationAction(
            'find_clinic',
            'Find Clinic',
            icon: DrawableResourceAndroidBitmap('ic_clinic'),
          ),
          const AndroidNotificationAction(
            'schedule_test',
            'Schedule Test',
            icon: DrawableResourceAndroidBitmap('ic_calendar'),
          ),
        ];
      case 'low risk':
        return [
          const AndroidNotificationAction(
            'learn_more',
            'Learn More',
            icon: DrawableResourceAndroidBitmap('ic_info'),
          ),
        ];
      default:
        return null;
    }
  }

  static Future<void> _scheduleFollowUpNotification(
    String riskLevel,
    int? userId,
  ) async {
    if (userId == null) return;

    try {
      final healthcareService = HealthcareProviderService();
      final providers = await healthcareService.getProvidersForRiskLevel(
        riskLevel,
      );

      if (providers.isNotEmpty) {
        final nearestProvider = providers.first;

        // Schedule notification for 1 hour later
        final scheduledTime = tz.TZDateTime.now(
          tz.local,
        ).add(const Duration(hours: 1));

        String title = '🏥 Healthcare Reminder';
        String body =
            'Don\'t forget to visit ${nearestProvider.name} for HIV testing. They are ${nearestProvider.distanceText} from you.';

        await _notificationsPlugin.zonedSchedule(
          DateTime.now().millisecondsSinceEpoch ~/ 1000 + 1,
          title,
          body,
          scheduledTime,
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'healthcare_reminder_channel',
              'Healthcare Reminders',
              channelDescription: 'Reminders for healthcare visits',
              importance: Importance.high,
              priority: Priority.high,
              icon: 'app_icon',
            ),
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: true,
            ),
          ),
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error scheduling follow-up notification: $e');
      }
    }
  }

  static Future<void> showHospitalDirectionNotification({
    required HealthcareProvider provider,
    required String riskLevel,
  }) async {
    String title = '🏥 ${provider.typeDisplayName} Found';
    String body =
        '${provider.name} is ${provider.distanceText} away.\n'
        '📍 ${provider.address}\n'
        '📞 ${provider.phone}\n'
        '⏰ ${provider.openingHours}';

    if (provider.hasHivTesting) {
      body += '\n✅ HIV Testing Available';
    }
    if (provider.hasHivTreatment) {
      body += '\n💊 HIV Treatment Available';
    }

    await _showSmartNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000 + 100,
      title: title,
      body: body,
      channelId: 'hospital_direction_channel',
      priority: Priority.high,
      riskLevel: riskLevel,
    );
  }
}
