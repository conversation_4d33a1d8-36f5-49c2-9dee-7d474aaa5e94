import 'package:flutter/material.dart';
import '../services/language_service.dart';

class LanguageProvider extends ChangeNotifier {
  final LanguageService _languageService = LanguageService();
  String _currentLanguage = 'en';
  Map<String, String> _currentTexts = {};

  String get currentLanguage => _currentLanguage;
  Map<String, String> get currentTexts => _currentTexts;

  LanguageProvider() {
    _initialize();
  }

  Future<void> _initialize() async {
    await _languageService.initialize();
    _currentLanguage = await _languageService.getCurrentLanguage();
    _currentTexts = _languageService.getTexts(_currentLanguage);
    notifyListeners();
  }

  Future<void> setLanguage(String languageCode) async {
    if (languageCode != _currentLanguage) {
      final success = await _languageService.setLanguage(languageCode);
      if (success) {
        _currentLanguage = languageCode;
        _currentTexts = _languageService.getTexts(languageCode);
        notifyListeners();
      }
    }
  }

  String getText(String key) {
    return _currentTexts[key] ?? key;
  }

  List<String> getSupportedLanguages() {
    return _languageService.getSupportedLanguages();
  }

  String getLanguageName(String languageCode) {
    return _languageService.getLanguageName(languageCode);
  }
}
