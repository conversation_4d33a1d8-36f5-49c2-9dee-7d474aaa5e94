import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../services/firebase_database_service.dart';
import '../models/user.dart' as models;
import 'login_screen.dart';
import 'admin_predictions_screen.dart';
import 'admin_users_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen>
    with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  final DatabaseService _databaseService = DatabaseService();
  final FirebaseDatabaseService _firebaseService = FirebaseDatabaseService();

  List<models.User> _users = [];
  List<Map<String, dynamic>> _allPredictions = [];
  Map<String, dynamic> _firebaseStats = {};
  bool _isLoading = true;
  bool _usingFirebase = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadAdminData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadAdminData() async {
    try {
      // Try Firebase first for comprehensive statistics
      List<Map<String, dynamic>> predictions;
      List<models.User> users;
      Map<String, dynamic> firebaseStats = {};

      try {
        // Use Firebase admin methods for enhanced statistics and real-time data
        firebaseStats = await _firebaseService.getAdminPredictionStats();
        predictions = await _firebaseService.getAllPredictionsWithUsers();
        final firebaseUsers = await _firebaseService.getAllUsers();

        // Convert Firebase user data to User models
        users = firebaseUsers
            .map(
              (userData) => models.User(
                id: userData['id'],
                username: userData['username'],
                email: userData['email'],
                fullName: userData['full_name'],
                createdAt: DateTime.parse(userData['created_at']),
                isAdmin: userData['is_admin'],
              ),
            )
            .toList();

        if (mounted) {
          setState(() {
            _users = users;
            _allPredictions = predictions;
            _firebaseStats = firebaseStats;
            _usingFirebase = true;
            _isLoading = false;
          });
        }

        print('✅ Firebase: Loaded comprehensive admin statistics');
        print('📊 Stats: ${firebaseStats.keys.join(", ")}');
      } catch (firebaseError) {
        print(
          '⚠️ Firebase failed, falling back to DatabaseService: $firebaseError',
        );

        // Fallback to DatabaseService if Firebase fails
        users = await _databaseService.getAllUsers();
        predictions = await _databaseService.getAllPredictionsWithUsers();

        if (mounted) {
          setState(() {
            _users = users;
            _allPredictions = predictions;
            _firebaseStats = {};
            _usingFirebase = false;
            _isLoading = false;
          });
        }

        print('✅ DatabaseService: Loaded basic admin data');
      }
    } catch (e) {
      print('❌ Error loading admin data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _signOut() {
    _authService.signOut();
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
    );
  }

  // Helper methods for manual statistics calculation (fallback when Firebase fails)
  Map<String, dynamic> _calculateManualStats() {
    return {
      'total_users': _users.length,
      'total_predictions': _allPredictions.length,
      'high_risk_count': _calculateHighRisk(),
      'medium_risk_count': _calculateMediumRisk(),
      'low_risk_count': _calculateLowRisk(),
      'today_predictions': _calculateTodayPredictions(),
      'week_predictions': _calculateWeekPredictions(),
      'month_predictions': _calculateMonthPredictions(),
    };
  }

  int _calculateHighRisk() {
    return _allPredictions
        .where((p) => p['prediction'].toString().toLowerCase().contains('high'))
        .length;
  }

  int _calculateMediumRisk() {
    return _allPredictions
        .where(
          (p) => p['prediction'].toString().toLowerCase().contains('medium'),
        )
        .length;
  }

  int _calculateLowRisk() {
    return _allPredictions
        .where((p) => p['prediction'].toString().toLowerCase().contains('low'))
        .length;
  }

  int _calculateTodayPredictions() {
    return _allPredictions.where((p) {
      final date = DateTime.parse(p['created_at']);
      final today = DateTime.now();
      return date.year == today.year &&
          date.month == today.month &&
          date.day == today.day;
    }).length;
  }

  int _calculateWeekPredictions() {
    return _allPredictions.where((p) {
      final date = DateTime.parse(p['created_at']);
      final now = DateTime.now();
      final weekAgo = now.subtract(const Duration(days: 7));
      return date.isAfter(weekAgo);
    }).length;
  }

  int _calculateMonthPredictions() {
    return _allPredictions.where((p) {
      final date = DateTime.parse(p['created_at']);
      final now = DateTime.now();
      final monthAgo = now.subtract(const Duration(days: 30));
      return date.isAfter(monthAgo);
    }).length;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<User?>(
      future: _authService.getCurrentUser(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        final user = snapshot.data;
        if (user == null || !user.isAdmin) {
          return const LoginScreen();
        }

        return _buildAdminDashboard(context, user);
      },
    );
  }

  Widget _buildAdminDashboard(BuildContext context, User user) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          slivers: [
            // App Bar
            SliverAppBar(
              expandedHeight: 200,
              floating: false,
              pinned: true,
              backgroundColor: const Color(0xFF1976D2),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'Admin Dashboard',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFF1976D2), Color(0xFF1565C0)],
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.admin_panel_settings,
                      size: 80,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: _isLoading
                      ? null
                      : () {
                          setState(() {
                            _isLoading = true;
                          });
                          _loadAdminData();
                        },
                  tooltip: 'Refresh Dashboard',
                ),
                IconButton(
                  icon: const Icon(Icons.logout, color: Colors.white),
                  onPressed: _signOut,
                  tooltip: 'Sign Out',
                ),
              ],
            ),

            // Content
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Statistics Overview
                    _buildStatisticsOverview(),
                    const SizedBox(height: 24),

                    // Quick Actions
                    _buildQuickActions(),
                    const SizedBox(height: 24),

                    // Recent Activity
                    _buildRecentActivity(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsOverview() {
    // Use Firebase comprehensive statistics if available, otherwise calculate manually
    final Map<String, dynamic> stats =
        _usingFirebase && _firebaseStats.isNotEmpty
        ? _firebaseStats
        : _calculateManualStats();

    final totalUsers = stats['total_users'] ?? _users.length;
    final totalPredictions =
        stats['total_predictions'] ?? _allPredictions.length;
    final highRiskPredictions =
        stats['high_risk_count'] ?? _calculateHighRisk();
    final mediumRiskPredictions =
        stats['medium_risk_count'] ?? _calculateMediumRisk();
    final lowRiskPredictions = stats['low_risk_count'] ?? _calculateLowRisk();
    final todayPredictions =
        stats['today_predictions'] ?? _calculateTodayPredictions();
    final thisWeekPredictions =
        stats['week_predictions'] ?? _calculateWeekPredictions();
    final thisMonthPredictions = stats['month_predictions'] ?? 0;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'System Overview',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1976D2),
                  ),
                ),
                const Spacer(),
                // Enhanced: Data source indicator
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _usingFirebase
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _usingFirebase
                          ? Colors.green.withValues(alpha: 0.3)
                          : Colors.orange.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _usingFirebase ? Icons.cloud_done : Icons.storage,
                        size: 14,
                        color: _usingFirebase
                            ? Colors.green[600]
                            : Colors.orange[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _usingFirebase ? 'Firebase' : 'Database',
                        style: TextStyle(
                          fontSize: 11,
                          color: _usingFirebase
                              ? Colors.green[600]
                              : Colors.orange[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.5,
                children: [
                  _buildStatCard(
                    'Total Users',
                    totalUsers.toString(),
                    Icons.people,
                    const Color(0xFF1976D2),
                  ),
                  _buildStatCard(
                    'Total Assessments',
                    totalPredictions.toString(),
                    Icons.assessment,
                    const Color(0xFF388E3C),
                  ),
                  _buildStatCard(
                    'High Risk Cases',
                    highRiskPredictions.toString(),
                    Icons.warning,
                    const Color(0xFFE53935),
                  ),
                  _buildStatCard(
                    'Today\'s Assessments',
                    todayPredictions.toString(),
                    Icons.today,
                    const Color(0xFFF57C00),
                  ),
                  _buildStatCard(
                    'Medium Risk Cases',
                    mediumRiskPredictions.toString(),
                    Icons.info,
                    const Color(0xFFFF9800),
                  ),
                  _buildStatCard(
                    'Low Risk Cases',
                    lowRiskPredictions.toString(),
                    Icons.check_circle,
                    const Color(0xFF4CAF50),
                  ),
                  _buildStatCard(
                    'This Week',
                    thisWeekPredictions.toString(),
                    Icons.date_range,
                    const Color(0xFF9C27B0),
                  ),
                  // Enhanced: Firebase-only statistic
                  if (_usingFirebase)
                    _buildStatCard(
                      'This Month',
                      thisMonthPredictions.toString(),
                      Icons.calendar_month,
                      const Color(0xFF795548),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 24, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1976D2),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.people,
                    label: 'Manage Users',
                    color: const Color(0xFF1976D2),
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AdminUsersScreen(),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.analytics,
                    label: 'View Predictions',
                    color: const Color(0xFF388E3C),
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AdminPredictionsScreen(),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(fontWeight: FontWeight.bold, color: color),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    final recentPredictions = _allPredictions.take(5).toList();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Recent Activity',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1976D2),
                  ),
                ),
                if (recentPredictions.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AdminPredictionsScreen(),
                        ),
                      );
                    },
                    child: const Text('View All'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (recentPredictions.isEmpty)
              Center(
                child: Text(
                  'No recent activity',
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
              )
            else
              ...recentPredictions.map((prediction) {
                return _buildActivityItem(prediction);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(Map<String, dynamic> prediction) {
    final riskLevel = prediction['prediction'].toString();
    Color riskColor;

    if (riskLevel.toLowerCase().contains('high')) {
      riskColor = Colors.red;
    } else if (riskLevel.toLowerCase().contains('medium')) {
      riskColor = Colors.orange;
    } else {
      riskColor = Colors.green;
    }

    final date = DateTime.parse(prediction['created_at']);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: riskColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(Icons.health_and_safety, color: riskColor, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${prediction['full_name']} - $riskLevel',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  'Confidence: ${(prediction['confidence'] * 100).toStringAsFixed(1)}%',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                _formatDateShort(date),
                style: TextStyle(color: Colors.grey[500], fontSize: 11),
              ),
              Text(
                '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}',
                style: TextStyle(color: Colors.grey[400], fontSize: 10),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDateShort(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${date.day}/${date.month}';
    }
  }
}
