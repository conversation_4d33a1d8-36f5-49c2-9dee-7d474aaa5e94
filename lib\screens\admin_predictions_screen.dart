import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/firebase_database_service.dart';

class AdminPredictionsScreen extends StatefulWidget {
  const AdminPredictionsScreen({super.key});

  @override
  State<AdminPredictionsScreen> createState() => _AdminPredictionsScreenState();
}

class _AdminPredictionsScreenState extends State<AdminPredictionsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final FirebaseDatabaseService _firebaseService = FirebaseDatabaseService();

  List<Map<String, dynamic>> _predictions = [];
  bool _isLoading = true;
  bool _usingFirebase = true;
  String _filterRisk = 'All';

  @override
  void initState() {
    super.initState();
    _loadPredictions();
  }

  Future<void> _loadPredictions() async {
    try {
      // Try Firebase first for real-time prediction data
      try {
        final predictions = await _firebaseService.getAllPredictionsWithUsers();

        setState(() {
          _predictions = predictions;
          _usingFirebase = true;
          _isLoading = false;
        });

        print(
          '✅ Firebase: Loaded ${predictions.length} predictions with user data',
        );
      } catch (firebaseError) {
        print(
          '⚠️ Firebase failed, falling back to DatabaseService: $firebaseError',
        );

        // Fallback to DatabaseService
        final predictions = await _databaseService.getAllPredictionsWithUsers();

        setState(() {
          _predictions = predictions;
          _usingFirebase = false;
          _isLoading = false;
        });

        print('✅ DatabaseService: Loaded ${predictions.length} predictions');
      }
    } catch (e) {
      print('❌ Error loading predictions: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> get _filteredPredictions {
    if (_filterRisk == 'All') {
      return _predictions;
    }

    // Enhanced filtering that works with both Firebase and DatabaseService data
    return _predictions.where((p) {
      final prediction = p['prediction']?.toString().toLowerCase() ?? '';
      final riskLevel = p['risk_level']?.toString().toLowerCase() ?? '';
      final filterLower = _filterRisk.toLowerCase();

      return prediction.contains(filterLower) ||
          riskLevel.contains(filterLower);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('All Predictions'),
            if (!_isLoading)
              Text(
                _usingFirebase
                    ? '${_filteredPredictions.length} predictions • Firebase Real-time'
                    : '${_filteredPredictions.length} predictions • Database Service',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.normal,
                ),
              ),
          ],
        ),
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading
                ? null
                : () {
                    setState(() {
                      _isLoading = true;
                    });
                    _loadPredictions();
                  },
            tooltip: 'Refresh Predictions',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onSelected: (value) {
              setState(() {
                _filterRisk = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'All', child: Text('All Risks')),
              const PopupMenuItem(value: 'High', child: Text('High Risk')),
              const PopupMenuItem(value: 'Medium', child: Text('Medium Risk')),
              const PopupMenuItem(value: 'Low', child: Text('Low Risk')),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1976D2), Colors.white],
            stops: [0.0, 0.3],
          ),
        ),
        child: Column(
          children: [
            // Statistics Header
            _buildStatisticsHeader(),

            // Predictions List
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    )
                  : _filteredPredictions.isEmpty
                  ? _buildEmptyState()
                  : _buildPredictionsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsHeader() {
    final totalPredictions = _predictions.length;
    final highRisk = _predictions
        .where((p) => p['prediction'].toString().toLowerCase().contains('high'))
        .length;
    final mediumRisk = _predictions
        .where(
          (p) => p['prediction'].toString().toLowerCase().contains('medium'),
        )
        .length;
    final lowRisk = _predictions
        .where((p) => p['prediction'].toString().toLowerCase().contains('low'))
        .length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Prediction Statistics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem('Total', totalPredictions, Colors.blue),
              ),
              Expanded(
                child: _buildStatItem('High Risk', highRisk, Colors.red),
              ),
              Expanded(
                child: _buildStatItem('Medium Risk', mediumRisk, Colors.orange),
              ),
              Expanded(
                child: _buildStatItem('Low Risk', lowRisk, Colors.green),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.assessment_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            _filterRisk == 'All'
                ? 'No predictions yet'
                : 'No $_filterRisk risk predictions',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredPredictions.length,
      itemBuilder: (context, index) {
        final prediction = _filteredPredictions[index];
        return _buildPredictionCard(prediction);
      },
    );
  }

  Widget _buildPredictionCard(Map<String, dynamic> prediction) {
    final riskLevel = prediction['prediction'].toString();
    Color riskColor;
    IconData riskIcon;

    if (riskLevel.toLowerCase().contains('high')) {
      riskColor = Colors.red;
      riskIcon = Icons.warning;
    } else if (riskLevel.toLowerCase().contains('medium')) {
      riskColor = Colors.orange;
      riskIcon = Icons.info;
    } else {
      riskColor = Colors.green;
      riskIcon = Icons.check_circle;
    }

    final date = DateTime.parse(prediction['created_at']);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: riskColor.withValues(alpha: 0.3), width: 2),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: riskColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(riskIcon, color: riskColor, size: 24),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          prediction['full_name'],
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '@${prediction['username']}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        riskLevel,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: riskColor,
                        ),
                      ),
                      Text(
                        '${(prediction['confidence'] * 100).toStringAsFixed(1)}%',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Details
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.email, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 8),
                        Text(
                          prediction['email'],
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const Spacer(),
                        Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(date),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    if (prediction['exposure_locations'] != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16,
                            color: Colors.orange[700],
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'High-risk exposure locations reported',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange[700],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
