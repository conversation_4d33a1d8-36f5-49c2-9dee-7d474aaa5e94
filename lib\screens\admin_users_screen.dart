import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/firebase_database_service.dart';
import '../models/user.dart';

class AdminUsersScreen extends StatefulWidget {
  const AdminUsersScreen({super.key});

  @override
  State<AdminUsersScreen> createState() => _AdminUsersScreenState();
}

class _AdminUsersScreenState extends State<AdminUsersScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final FirebaseDatabaseService _firebaseService = FirebaseDatabaseService();

  List<User> _users = [];
  List<Map<String, dynamic>> _userStats =
      []; // Enhanced user data with prediction counts
  bool _isLoading = true;
  bool _usingFirebase = true; // Track which service is being used

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      // Try Firebase first for enhanced user data with prediction counts
      try {
        final firebaseUsers = await _firebaseService.getAllUsers();

        // Convert Firebase user data to User models and store enhanced stats
        final users = firebaseUsers
            .map(
              (userData) => User(
                id: userData['id'],
                username: userData['username'],
                email: userData['email'],
                fullName: userData['full_name'],
                createdAt: DateTime.parse(userData['created_at']),
                isAdmin: userData['is_admin'],
              ),
            )
            .toList();

        setState(() {
          _users = users;
          _userStats = firebaseUsers; // Store enhanced data for display
          _usingFirebase = true;
          _isLoading = false;
        });

        // Firebase: Loaded users with enhanced data
      } catch (firebaseError) {
        // Firebase failed, falling back to DatabaseService

        // Fallback to DatabaseService
        final users = await _databaseService.getAllUsers();

        setState(() {
          _users = users;
          _userStats = []; // No enhanced data available
          _usingFirebase = false;
          _isLoading = false;
        });

        // DatabaseService: Loaded users (basic data)
      }
    } catch (e) {
      // Error loading users
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Manage Users'),
            if (!_isLoading)
              Text(
                _usingFirebase
                    ? '${_users.length} users • Firebase Real-time'
                    : '${_users.length} users • Database Service',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.normal,
                ),
              ),
          ],
        ),
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading
                ? null
                : () {
                    setState(() {
                      _isLoading = true;
                    });
                    _loadUsers();
                  },
            tooltip: 'Refresh Users',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1976D2), Colors.white],
            stops: [0.0, 0.3],
          ),
        ),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: Colors.white),
              )
            : _users.isEmpty
            ? _buildEmptyState()
            : _buildUsersList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No users found',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersList() {
    return RefreshIndicator(
      onRefresh: _loadUsers,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _users.length,
        itemBuilder: (context, index) {
          final user = _users[index];
          return _buildUserCard(user);
        },
      ),
    );
  }

  Widget _buildUserCard(User user) {
    // Find enhanced data for this user if available
    final userStats = _userStats.isNotEmpty
        ? _userStats.firstWhere(
            (stats) => stats['id'] == user.id,
            orElse: () => <String, dynamic>{},
          )
        : <String, dynamic>{};

    final predictionCount = userStats['prediction_count'] ?? 0;
    final lastLoginAt = userStats['last_login_at'];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with enhanced info
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: user.isAdmin
                        ? Colors.red.withValues(alpha: 0.2)
                        : const Color(0xFF1976D2).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    user.isAdmin ? Icons.admin_panel_settings : Icons.person,
                    color: user.isAdmin ? Colors.red : const Color(0xFF1976D2),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.fullName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '@${user.username}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                // Enhanced: Show prediction count badge
                if (_usingFirebase && predictionCount > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1976D2).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFF1976D2).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      '$predictionCount predictions',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Color(0xFF1976D2),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                if (user.isAdmin)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: const Text(
                      'ADMIN',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Enhanced User Details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildDetailRow('Email', user.email),
                  const SizedBox(height: 8),
                  _buildDetailRow('Username', user.username),
                  const SizedBox(height: 8),
                  _buildDetailRow('Joined', _formatDate(user.createdAt)),
                  if (_usingFirebase && lastLoginAt != null) ...[
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      'Last Login',
                      _formatDate(DateTime.parse(lastLoginAt)),
                    ),
                  ],
                  if (_usingFirebase) ...[
                    const SizedBox(height: 8),
                    _buildDetailRow('Predictions', predictionCount.toString()),
                  ],
                  const SizedBox(height: 8),
                  _buildDetailRow('User ID', user.id.toString()),
                ],
              ),
            ),

            // Enhanced: Data source indicator
            if (_usingFirebase)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Row(
                  children: [
                    Icon(Icons.cloud_done, size: 16, color: Colors.green[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Real-time Firebase data',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )
            else
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Row(
                  children: [
                    Icon(Icons.storage, size: 16, color: Colors.orange[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Database service data',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
              fontSize: 12,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
