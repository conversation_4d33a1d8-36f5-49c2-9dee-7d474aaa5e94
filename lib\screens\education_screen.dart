import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:video_player/video_player.dart';
import 'package:url_launcher/url_launcher.dart';

class EducationScreen extends StatefulWidget {
  const EducationScreen({super.key});

  @override
  State<EducationScreen> createState() => _EducationScreenState();
}

class _EducationScreenState extends State<EducationScreen> {
  final TextEditingController _searchController = TextEditingController();
  final List<Map<String, dynamic>> educationTopics = [
    {
      'title': 'What is HIV?',
      'description':
          'HIV (Human Immunodeficiency Virus) is a virus that attacks the body\'s immune system, specifically CD4 cells. If not treated, HIV can lead to AIDS (Acquired Immunodeficiency Syndrome). HIV is a lifelong condition, but with proper medical care, it can be controlled. The virus was first identified in 1981 and has since become a global health concern affecting millions of people worldwide.\n\nPractical Tips:\n• Get tested regularly if you\'re sexually active\n• Know your status to protect yourself and others\n• Early detection leads to better outcomes',
      'image': 'assets/images/hiv_1.png',
      'videoUrl': 'assets/videos/hiv_intro.mp4',
      'onlineVideoUrl': 'https://www.youtube.com/watch?v=8dnpqKJc5vU',
      'externalLinks': [
        {
          'title': 'WHO HIV/AIDS Information',
          'url': 'https://www.who.int/news-room/fact-sheets/detail/hiv-aids',
        },
        {
          'title': 'CDC HIV Basics',
          'url': 'https://www.cdc.gov/hiv/basics/index.html',
        },
      ],
    },
    {
      'title': 'How HIV Affects the Body',
      'description':
          'HIV targets and destroys CD4 cells, which are crucial for fighting infections. As the virus multiplies, it weakens the immune system, making the body vulnerable to opportunistic infections and certain cancers. The progression of HIV occurs in three stages: Acute HIV infection, Chronic HIV infection, and AIDS. Early detection and treatment can significantly slow down this progression.\n\nKey Symptoms to Watch For:\n• Flu-like symptoms 2-4 weeks after infection\n• Persistent fever and night sweats\n• Unexplained weight loss\n• Chronic fatigue',
      'image': 'assets/images/hiv_2.png',
      'videoUrl': null,
      'onlineVideoUrl': 'https://www.youtube.com/watch?v=1iA1xXwqT3A',
      'externalLinks': [
        {
          'title': 'HIV and Your Body',
          'url':
              'https://www.hiv.gov/hiv-basics/overview/about-hiv-and-aids/how-is-hiv-transmitted',
        },
        {
          'title': 'Understanding HIV Progression',
          'url': 'https://www.aidsmap.com/about-hiv/hiv-progression',
        },
      ],
    },
    {
      'title': 'HIV Transmission and Prevention',
      'description':
          'HIV can be transmitted through specific bodily fluids including blood, semen, pre-seminal fluid, rectal fluids, vaginal fluids, and breast milk. Prevention methods include using condoms, PrEP (pre-exposure prophylaxis), and sterile needles. HIV cannot be transmitted through casual contact, air, water, or insect bites. Regular testing and early treatment are crucial prevention strategies.\n\nPrevention Checklist:\n✓ Use condoms consistently and correctly\n✓ Get tested regularly\n✓ Consider PrEP if at high risk\n✓ Never share needles or syringes\n✓ Get vaccinated against other infections',
      'image': 'assets/images/hiv_3.png',
      'videoUrl': null,
      'onlineVideoUrl': 'https://www.youtube.com/watch?v=8dnpqKJc5vU',
      'externalLinks': [
        {
          'title': 'HIV Prevention Methods',
          'url': 'https://www.cdc.gov/hiv/basics/prevention.html',
        },
        {
          'title': 'PrEP Information',
          'url': 'https://www.cdc.gov/hiv/basics/prep.html',
        },
      ],
    },
    {
      'title': 'Living with HIV',
      'description':
          'With proper medical care and antiretroviral therapy (ART), people with HIV can live long, healthy lives. ART reduces the amount of HIV in the body to undetectable levels, preventing transmission and maintaining immune function. Regular medical check-ups, a healthy lifestyle, and emotional support are essential for managing HIV effectively.\n\nDaily Living Tips:\n• Take medications at the same time daily\n• Maintain a healthy diet and exercise routine\n• Get adequate sleep and manage stress\n• Keep all medical appointments\n• Build a support network',
      'image': 'assets/images/hiv_4.png',
      'videoUrl': null,
    },
    {
      'title': 'HIV Testing and Diagnosis',
      'description':
          'HIV testing is crucial for early detection and treatment. Modern tests can detect HIV as early as 23 days after exposure. Types of tests include antibody tests, antigen/antibody tests, and nucleic acid tests. Early diagnosis leads to better health outcomes and helps prevent transmission to others.\n\nTesting Guide:\n• Where to get tested: clinics, hospitals, or home testing kits\n• Types of tests available\n• What to expect during testing\n• Understanding your results\n• Next steps after diagnosis',
      'image': 'assets/images/hiv_1.png',
      'videoUrl': null,
    },
    {
      'title': 'Treatment Options',
      'description':
          'Antiretroviral therapy (ART) is the primary treatment for HIV. It involves taking a combination of HIV medicines daily. Different classes of HIV medicines include NRTIs, NNRTIs, PIs, and INSTIs. Treatment adherence is crucial for maintaining viral suppression and preventing drug resistance.\n\nTreatment Success Tips:\n• Set daily medication reminders\n• Use pill organizers\n• Keep a medication journal\n• Report side effects to your doctor\n• Never skip doses',
      'image': 'assets/images/hiv_2.png',
      'videoUrl': null,
    },
    {
      'title': 'Mental Health and Support',
      'description':
          'Living with HIV can affect mental health. Common challenges include anxiety, depression, and stigma. Support groups, counseling, and mental health services are available to help. Building a strong support network and maintaining open communication with healthcare providers is essential for emotional well-being.\n\nSupport Resources:\n• Local support groups\n• Online communities\n• Mental health professionals\n• Crisis hotlines\n• Peer support programs',
      'image': 'assets/images/hiv_3.png',
      'videoUrl': null,
    },
    {
      'title': 'HIV and Pregnancy',
      'description':
          'With proper medical care, women with HIV can have healthy pregnancies and prevent transmission to their babies. This includes taking HIV medicines during pregnancy, delivery, and after birth. The risk of transmission can be reduced to less than 1% with appropriate medical care.\n\nPregnancy Care Checklist:\n• Regular prenatal care\n• Consistent ART adherence\n• Safe delivery planning\n• Postpartum care for mother and baby\n• Infant feeding guidance',
      'image': 'assets/images/hiv_4.png',
      'videoUrl': null,
    },
  ];

  List<Map<String, dynamic>> filteredTopics = [];
  Set<String> bookmarkedTitles = {};

  @override
  void initState() {
    super.initState();
    filteredTopics = educationTopics;
    _loadBookmarks();
    _searchController.addListener(() {
      final query = _searchController.text.toLowerCase();
      setState(() {
        filteredTopics = educationTopics
            .where((topic) => topic['title']!.toLowerCase().contains(query))
            .toList();
      });
    });
  }

  Future<void> _loadBookmarks() async {
    final prefs = await SharedPreferences.getInstance();
    final saved = prefs.getStringList('bookmarks') ?? [];
    setState(() => bookmarkedTitles = saved.toSet());
  }

  Future<void> _toggleBookmark(String title) async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      if (bookmarkedTitles.contains(title)) {
        bookmarkedTitles.remove(title);
      } else {
        bookmarkedTitles.add(title);
      }
      prefs.setStringList('bookmarks', bookmarkedTitles.toList());
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HIV Education'),
        backgroundColor: Colors.red,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search topics...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: filteredTopics.length,
              itemBuilder: (context, index) {
                final topic = filteredTopics[index];
                return AnimatedTopicCard(
                  child: Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 4,
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.asset(
                          topic['image']!,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                        ),
                      ),
                      title: Text(
                        topic['title']!,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      subtitle: Text(
                        topic['description']!.split('\n\n')[0],
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      trailing: IconButton(
                        icon: Icon(
                          bookmarkedTitles.contains(topic['title'])
                              ? Icons.bookmark
                              : Icons.bookmark_border,
                          color: Colors.red,
                        ),
                        onPressed: () => _toggleBookmark(topic['title']!),
                      ),
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => EducationDetailScreen(topic: topic),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class EducationDetailScreen extends StatefulWidget {
  final Map<String, dynamic> topic;
  const EducationDetailScreen({super.key, required this.topic});

  @override
  State<EducationDetailScreen> createState() => _EducationDetailScreenState();
}

class _EducationDetailScreenState extends State<EducationDetailScreen> {
  VideoPlayerController? _controller;
  bool _isLocalVideo = false;

  @override
  void initState() {
    super.initState();
    if (widget.topic['videoUrl'] != null) {
      try {
        _controller = VideoPlayerController.asset(widget.topic['videoUrl']!)
          ..initialize()
              .then((_) {
                if (mounted) {
                  setState(() {
                    _isLocalVideo = true;
                    _controller!.play();
                  });
                }
              })
              .catchError((error) {
                debugPrint('Error loading local video: $error');
                if (mounted) {
                  setState(() {
                    _isLocalVideo = false;
                  });
                }
              });
      } catch (e) {
        debugPrint('Error initializing video controller: $e');
        if (mounted) {
          setState(() {
            _isLocalVideo = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not launch the URL'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final descriptionParts = widget.topic['description']!.split('\n\n');
    final mainDescription = descriptionParts[0];
    final practicalTips = descriptionParts.length > 1
        ? descriptionParts[1]
        : '';

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.topic['title']!),
        backgroundColor: Colors.red,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.asset(
                widget.topic['image']!,
                width: double.infinity,
                height: 200,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              widget.topic['title']!,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              mainDescription,
              style: const TextStyle(fontSize: 16, height: 1.5),
            ),
            if (practicalTips.isNotEmpty) ...[
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Practical Tips',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      practicalTips,
                      style: const TextStyle(fontSize: 16, height: 1.5),
                    ),
                  ],
                ),
              ),
            ],
            if (widget.topic['onlineVideoUrl'] != null) ...[
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Educational Video',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: () =>
                          _launchUrl(widget.topic['onlineVideoUrl']),
                      icon: const Icon(Icons.play_circle_outline),
                      label: const Text('Watch Educational Video'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (widget.topic['externalLinks'] != null) ...[
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Additional Resources',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...widget.topic['externalLinks'].map<Widget>(
                      (link) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: ElevatedButton.icon(
                          onPressed: () => _launchUrl(link['url']),
                          icon: const Icon(Icons.link),
                          label: Text(link['title']),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 20),
            if (_isLocalVideo &&
                _controller != null &&
                _controller!.value.isInitialized)
              AspectRatio(
                aspectRatio: _controller!.value.aspectRatio,
                child: VideoPlayer(_controller!),
              )
            else if (widget.topic['videoUrl'] != null && !_isLocalVideo)
              const Center(child: Text('Video not available in this format')),
          ],
        ),
      ),
      floatingActionButton: _isLocalVideo && _controller != null
          ? FloatingActionButton(
              onPressed: () {
                setState(() {
                  _controller!.value.isPlaying
                      ? _controller!.pause()
                      : _controller!.play();
                });
              },
              child: Icon(
                _controller!.value.isPlaying ? Icons.pause : Icons.play_arrow,
              ),
            )
          : null,
    );
  }
}

class AnimatedTopicCard extends StatefulWidget {
  final Widget child;
  const AnimatedTopicCard({required this.child, super.key});

  @override
  State<AnimatedTopicCard> createState() => _AnimatedTopicCardState();
}

class _AnimatedTopicCardState extends State<AnimatedTopicCard>
    with SingleTickerProviderStateMixin {
  double opacity = 0;

  @override
  void initState() {
    super.initState();
    Future.delayed(
      const Duration(milliseconds: 200),
      () => setState(() => opacity = 1),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 700),
      opacity: opacity,
      child: widget.child,
    );
  }
}
