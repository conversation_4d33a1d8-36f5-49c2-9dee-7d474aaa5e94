import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../services/hybrid_prediction_service.dart';
import '../notifications/notification_service.dart';

import 'login_screen.dart';
import 'dashboard_screen.dart';
import 'map_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Enhanced symptom tracking with clear descriptions and severity levels
  final Map<String, int> _symptoms = {
    // Physical symptoms with temperature/severity indicators
    'fever':
        0, // Body temperature: Normal (<37.5°C) / Mild fever (37.5-38.5°C) / High fever (>38.5°C)
    'headache':
        0, // Head pain: None / Moderate (manageable) / Severe (affects daily life)
    'muscle_pain':
        0, // Body aches: None / Mild (like after exercise) / Severe (difficulty moving)
    'skin_rash':
        0, // Skin changes: None / Small occasional rashes / Large persistent rashes
    'weight_loss':
        0, // Unintentional: None / 2-5kg in 3 months / More than 5kg in 3 months
    'fatigue':
        0, // Tiredness: Normal energy / More tired than usual / Extreme exhaustion
    'night_sweats':
        0, // Sleep sweating: None / Occasional (1-2/week) / Frequent (need to change clothes)
    'swollen_lymph_nodes':
        0, // Neck/armpit/groin swelling: None / Small tender / Large persistent
    // Digestive and respiratory symptoms
    'persistent_diarrhea':
        0, // Loose stools: None / Occasional / Daily for 2+ weeks
    'persistent_cough': 0, // Dry cough: None / Sometimes / Daily for 3+ weeks
    'shortness_of_breath':
        0, // Breathing difficulty: None / With exercise / During normal activities
    'nausea_vomiting':
        0, // Stomach upset: None / Sometimes / Frequent unexplained
    'loss_of_appetite':
        0, // Eating: Normal / Decreased interest / Significant decrease
    // Oral and throat symptoms
    'oral_ulcers':
        0, // Mouth sores: None / Occasional / Painful sores that don't heal
    'persistent_sore_throat':
        0, // Throat pain: None / Sometimes / Daily for 2+ weeks
    'white_patches_mouth':
        0, // Thrush/candida: None / Small patches / Large white areas
    // Neurological symptoms
    'memory_problems':
        0, // Thinking: Normal / Sometimes forgetful / Difficulty with recent events
    'confusion':
        0, // Mental clarity: Clear thinking / Sometimes confused / Difficulty concentrating
    // Advanced symptoms
    'recurring_infections':
        0, // Getting sick: Normal / More often than usual / Frequent infections
    'unexplained_bruising':
        0, // Skin bruising: Normal / Easy bruising / Unexplained large bruises
    'vision_problems':
        0, // Eyesight: Normal / Sometimes blurry / Persistent vision issues
    // Risk behaviors with clear frequency indicators
    'unprotected_sex':
        0, // Protection use: Always use / Sometimes don't use / Frequently don't use
    'multiple_partners': 0, // Partners in 6 months: 0-1 / 2-3 / 4 or more
    'shared_needles': 0, // Needle sharing: Never / Rarely / Sometimes or often
    'blood_transfusion':
        0, // Recent transfusion: None / In past year / In past 6 months
    'tattoo_piercing_unsterile':
        0, // Unsterile procedures: None / Possible / Definitely unsterile
    'partner_hiv_positive':
        0, // Partner status: Negative/unknown / Possibly positive / Known positive
  };

  // Location tracking for high-risk behaviors (where exposure might have occurred)
  final Map<String, String> _exposureLocations = {
    'unprotected_sex': '',
    'multiple_partners': '',
    'shared_needles': '',
    'blood_transfusion': '',
    'tattoo_piercing_unsterile': '',
    'partner_hiv_positive': '',
  };

  bool _isLoading = false;
  String? _predictionResult;
  double? _confidence;
  Map<String, dynamic>? _serviceStatus;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
    _loadServiceStatus();
  }

  Future<void> _loadServiceStatus() async {
    final status = await HybridPredictionService.getServiceStatus();
    if (mounted) {
      setState(() {
        _serviceStatus = status;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _submitAssessment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = await _authService.getCurrentUser();

      Map<String, dynamic> result;

      if (user != null) {
        // Authenticated user - save to database
        final requestData = {
          'symptoms': _symptoms,
          'exposure_locations': _exposureLocations,
          'user_id': user.id,
        };

        result = await HybridPredictionService.predictHivWithLocations(
          requestData,
        );

        // Send notification for medium/high risk users
        await _sendRiskNotification(user.id);
      } else {
        // Anonymous user - just get prediction without saving
        result = await HybridPredictionService.predictHiv(_symptoms);
      }

      setState(() {
        _predictionResult = result['prediction'];
        _confidence = result['confidence'];
        _isLoading = false;
      });

      // Show result dialog
      _showResultDialog();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _sendRiskNotification(int userId) async {
    if (_predictionResult == null) return;

    final riskLevel = _predictionResult!.split(' ').first;

    // Send notification for medium and high risk users
    if (riskLevel == 'Medium' || riskLevel == 'High') {
      String title = riskLevel == 'High'
          ? '🚨 High HIV Risk Detected'
          : '⚠️ Medium HIV Risk Detected';

      String body = riskLevel == 'High'
          ? 'Immediate medical attention recommended. Tap to find nearest hospital.'
          : 'HIV testing recommended. Tap to find testing centers nearby.';

      await NotificationService.showNotification(title: title, body: body);
    }
  }

  void _showResultDialog() {
    Color riskColor;
    IconData riskIcon;
    String riskLevel = _predictionResult?.split(' ').first ?? 'Unknown';

    switch (riskLevel) {
      case 'High':
        riskColor = Colors.red;
        riskIcon = Icons.warning;
        break;
      case 'Medium':
        riskColor = Colors.orange;
        riskIcon = Icons.info;
        break;
      case 'Low':
        riskColor = Colors.green;
        riskIcon = Icons.check_circle;
        break;
      default:
        riskColor = Colors.grey;
        riskIcon = Icons.help;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(riskIcon, color: riskColor, size: 28),
            const SizedBox(width: 12),
            Text(
              'Assessment Result',
              style: TextStyle(color: riskColor, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Risk Level: $_predictionResult',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: riskColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Confidence: ${(_confidence! * 100).toStringAsFixed(1)}%',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),

            // Risk-specific recommendations
            _buildRiskRecommendations(riskLevel),

            const SizedBox(height: 16),
            FutureBuilder<User?>(
              future: _authService.getCurrentUser(),
              builder: (context, snapshot) {
                final user = snapshot.data;
                if (user != null) {
                  return Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.green.withValues(alpha: 0.3),
                      ),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green, size: 20),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Assessment saved to your history. Consult with a healthcare professional for medical advice.',
                            style: TextStyle(fontSize: 14, color: Colors.green),
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  return Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.blue.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue, size: 20),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Assessment not saved',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Sign up to save your results, track your health over time, and get personalized recommendations.',
                          style: TextStyle(fontSize: 14, color: Colors.blue),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(context).pop();
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => const LoginScreen(),
                              ),
                            );
                          },
                          icon: const Icon(Icons.login, size: 16),
                          label: const Text('Sign Up / Sign In'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
              },
            ),
          ],
        ),
        actions: [
          // Hospital navigation for medium/high risk
          if (riskLevel == 'Medium' || riskLevel == 'High')
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToHospitals();
              },
              icon: const Icon(Icons.local_hospital),
              label: Text(
                riskLevel == 'High' ? 'Find Hospital' : 'Find Testing Center',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),

          FutureBuilder<User?>(
            future: _authService.getCurrentUser(),
            builder: (context, snapshot) {
              final user = snapshot.data;
              if (user != null) {
                return TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(
                        builder: (context) => const DashboardScreen(),
                      ),
                    );
                  },
                  child: const Text('View Dashboard'),
                );
              } else {
                return TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Close'),
                );
              }
            },
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetForm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1976D2),
              foregroundColor: Colors.white,
            ),
            child: const Text('New Assessment'),
          ),
        ],
      ),
    );
  }

  Widget _buildRiskRecommendations(String riskLevel) {
    switch (riskLevel) {
      case 'High':
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.warning, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Immediate Action Recommended',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                '• Visit a healthcare provider immediately\n'
                '• Get tested for HIV as soon as possible\n'
                '• Consider PEP (Post-Exposure Prophylaxis) if recent exposure\n'
                '• Use protection in future encounters',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        );
      case 'Medium':
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info, color: Colors.orange, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Testing Recommended',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                '• Schedule an HIV test within the next few weeks\n'
                '• Visit a testing center or healthcare provider\n'
                '• Practice safe behaviors\n'
                '• Consider regular testing if at ongoing risk',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        );
      default:
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Continue Prevention',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                '• Maintain safe practices\n'
                '• Consider regular testing as part of routine healthcare\n'
                '• Stay informed about HIV prevention\n'
                '• Support others in your community',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        );
    }
  }

  void _navigateToHospitals() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const MapScreen()));
  }

  void _resetForm() {
    setState(() {
      _symptoms.updateAll((key, value) => 0);
      _exposureLocations.updateAll((key, value) => '');
      _predictionResult = null;
      _confidence = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HIV Risk Assessment'),
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await _authService.signOut();
              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const LoginScreen()),
                  (route) => false,
                );
              }
            },
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFF1976D2), Colors.white],
              stops: [0.0, 0.3],
            ),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'HIV Risk Assessment',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Please answer the following questions honestly. This assessment is confidential and will help determine your HIV risk level.',
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Service Status
                if (_serviceStatus != null) _buildServiceStatusCard(),
                const SizedBox(height: 24),

                // Physical Symptoms Section with Enhanced Descriptions
                _buildSectionCard(
                  title: 'Physical Symptoms',
                  subtitle: 'Rate each symptom: No / Sometimes / Yes',
                  icon: Icons.health_and_safety,
                  children: [
                    _buildEnhancedSymptomTile(
                      'fever',
                      'Fever (Body Temperature)',
                      'Normal (<37.5°C) → Mild fever (37.5-38.5°C) → High fever (>38.5°C)',
                    ),
                    _buildEnhancedSymptomTile(
                      'headache',
                      'Headache',
                      'None → Moderate (manageable) → Severe (affects daily life)',
                    ),
                    _buildEnhancedSymptomTile(
                      'muscle_pain',
                      'Muscle Pain',
                      'None → Mild (like after exercise) → Severe (difficulty moving)',
                    ),
                    _buildEnhancedSymptomTile(
                      'skin_rash',
                      'Skin Rash',
                      'None → Small occasional rashes → Large persistent rashes',
                    ),
                    _buildEnhancedSymptomTile(
                      'weight_loss',
                      'Unintentional Weight Loss',
                      'None → 2-5kg in 3 months → More than 5kg in 3 months',
                    ),
                    _buildEnhancedSymptomTile(
                      'fatigue',
                      'Fatigue/Tiredness',
                      'Normal energy → More tired than usual → Extreme exhaustion',
                    ),
                    _buildEnhancedSymptomTile(
                      'night_sweats',
                      'Night Sweats',
                      'None → Occasional (1-2/week) → Frequent (need to change clothes)',
                    ),
                    _buildEnhancedSymptomTile(
                      'swollen_lymph_nodes',
                      'Swollen Lymph Nodes',
                      'None → Small tender swellings → Large persistent swellings',
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Digestive and Respiratory Symptoms Section
                _buildSectionCard(
                  title: 'Digestive & Respiratory Symptoms',
                  subtitle: 'Symptoms affecting digestion and breathing',
                  icon: Icons.medical_information,
                  children: [
                    _buildEnhancedSymptomTile(
                      'persistent_diarrhea',
                      'Persistent Diarrhea',
                      'None → Occasional loose stools → Daily for 2+ weeks',
                    ),
                    _buildEnhancedSymptomTile(
                      'persistent_cough',
                      'Persistent Cough',
                      'None → Sometimes coughing → Daily dry cough for 3+ weeks',
                    ),
                    _buildEnhancedSymptomTile(
                      'shortness_of_breath',
                      'Shortness of Breath',
                      'None → With exercise → During normal activities',
                    ),
                    _buildEnhancedSymptomTile(
                      'nausea_vomiting',
                      'Nausea/Vomiting',
                      'None → Sometimes upset stomach → Frequent unexplained nausea',
                    ),
                    _buildEnhancedSymptomTile(
                      'loss_of_appetite',
                      'Loss of Appetite',
                      'Normal eating → Decreased interest in food → Significant decrease',
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Oral, Neurological & Advanced Symptoms Section
                _buildSectionCard(
                  title: 'Oral, Neurological & Advanced Symptoms',
                  subtitle:
                      'More specific symptoms that may indicate complications',
                  icon: Icons.priority_high,
                  children: [
                    _buildEnhancedSymptomTile(
                      'oral_ulcers',
                      'Oral Ulcers (Mouth Sores)',
                      'None → Occasional small sores → Painful sores that don\'t heal',
                    ),
                    _buildEnhancedSymptomTile(
                      'persistent_sore_throat',
                      'Persistent Sore Throat',
                      'None → Sometimes sore → Daily pain for 2+ weeks',
                    ),
                    _buildEnhancedSymptomTile(
                      'white_patches_mouth',
                      'White Patches in Mouth',
                      'None → Small white spots → Large white areas (thrush)',
                    ),
                    _buildEnhancedSymptomTile(
                      'memory_problems',
                      'Memory Problems',
                      'Normal thinking → Sometimes forgetful → Difficulty with recent events',
                    ),
                    _buildEnhancedSymptomTile(
                      'confusion',
                      'Confusion/Concentration',
                      'Clear thinking → Sometimes confused → Difficulty concentrating',
                    ),
                    _buildEnhancedSymptomTile(
                      'recurring_infections',
                      'Recurring Infections',
                      'Normal immunity → More sick than usual → Frequent infections',
                    ),
                    _buildEnhancedSymptomTile(
                      'unexplained_bruising',
                      'Unexplained Bruising',
                      'Normal bruising → Easy bruising → Large unexplained bruises',
                    ),
                    _buildEnhancedSymptomTile(
                      'vision_problems',
                      'Vision Problems',
                      'Normal eyesight → Sometimes blurry → Persistent vision issues',
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Risk Behaviors Section
                _buildSectionCard(
                  title: 'Risk Behaviors & Exposure History',
                  subtitle:
                      'Please answer honestly about activities in the past 6 months',
                  icon: Icons.warning,
                  children: [
                    _buildEnhancedRiskBehaviorTile(
                      'unprotected_sex',
                      'Unprotected Sexual Activity',
                      'Always use protection → Sometimes don\'t use → Frequently don\'t use',
                    ),
                    _buildEnhancedRiskBehaviorTile(
                      'multiple_partners',
                      'Multiple Sexual Partners',
                      '0-1 partners → 2-3 partners → 4+ partners in 6 months',
                    ),
                    _buildEnhancedRiskBehaviorTile(
                      'shared_needles',
                      'Shared Needles/Syringes',
                      'Never shared → Rarely shared → Sometimes/often shared',
                    ),
                    _buildEnhancedRiskBehaviorTile(
                      'blood_transfusion',
                      'Recent Blood Transfusion',
                      'None → In past year → In past 6 months',
                    ),
                    _buildEnhancedRiskBehaviorTile(
                      'tattoo_piercing_unsterile',
                      'Unsterile Tattoo/Piercing',
                      'None → Possibly unsterile → Definitely unsterile equipment',
                    ),
                    _buildEnhancedRiskBehaviorTile(
                      'partner_hiv_positive',
                      'Partner HIV Status',
                      'Negative/unknown → Possibly positive → Known positive',
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _submitAssessment,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFE53935),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                      elevation: 4,
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                            'Get Assessment Result',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                const SizedBox(height: 16),

                // Disclaimer
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.orange.withValues(alpha: 0.3),
                    ),
                  ),
                  child: const Column(
                    children: [
                      Icon(Icons.info, color: Colors.orange, size: 24),
                      SizedBox(height: 8),
                      Text(
                        'Important Disclaimer',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'This assessment is for informational purposes only and should not replace professional medical advice. Please consult with a healthcare provider for proper testing and diagnosis.',
                        style: TextStyle(fontSize: 14),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildServiceStatusCard() {
    final status = _serviceStatus!;
    final isOnline = status['mode'] == 'online';
    final icon = status['icon'] ?? (isOnline ? '🤖' : '📱');
    final message = status['message'] ?? 'Unknown status';
    final accuracy = status['accuracy'] ?? 'Unknown';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isOnline
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isOnline
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.blue.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Text(icon, style: const TextStyle(fontSize: 24)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isOnline ? Colors.green[700] : Colors.blue[700],
                  ),
                ),
                Text(
                  'Accuracy: $accuracy',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          if (!isOnline)
            TextButton(
              onPressed: () {
                HybridPredictionService.forceRetryOnline();
                _loadServiceStatus();
              },
              child: const Text('Retry'),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1976D2).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: const Color(0xFF1976D2)),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedSymptomTile(
    String key,
    String label,
    String description,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildOptionButton(key, 0, 'No', Colors.green)),
              const SizedBox(width: 8),
              Expanded(
                child: _buildOptionButton(key, 1, 'Sometimes', Colors.orange),
              ),
              const SizedBox(width: 8),
              Expanded(child: _buildOptionButton(key, 2, 'Yes', Colors.red)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedRiskBehaviorTile(
    String key,
    String label,
    String description,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildOptionButton(key, 0, 'No', Colors.green)),
              const SizedBox(width: 8),
              Expanded(
                child: _buildOptionButton(key, 1, 'Sometimes', Colors.orange),
              ),
              const SizedBox(width: 8),
              Expanded(child: _buildOptionButton(key, 2, 'Yes', Colors.red)),
            ],
          ),
          // Location input for high-risk behaviors
          if (_symptoms[key] == 2) ...[
            const SizedBox(height: 12),
            TextField(
              decoration: InputDecoration(
                labelText: 'Location (optional)',
                hintText: 'Where did this occur?',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _exposureLocations[key] = value;
                });
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOptionButton(String key, int value, String label, Color color) {
    bool isSelected = _symptoms[key] == value;
    return GestureDetector(
      onTap: () {
        setState(() {
          _symptoms[key] = value;
          // Clear location if not high risk
          if (value != 2) {
            _exposureLocations[key] = '';
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
