import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/healthcare_provider.dart';
import '../services/healthcare_provider_service.dart';
import '../notifications/notification_service.dart';

class HospitalFinderScreen extends StatefulWidget {
  final String? riskLevel;
  final String? userLocation;

  const HospitalFinderScreen({super.key, this.riskLevel, this.userLocation});

  @override
  State<HospitalFinderScreen> createState() => _HospitalFinderScreenState();
}

class _HospitalFinderScreenState extends State<HospitalFinderScreen> {
  final HealthcareProviderService _healthcareService =
      HealthcareProviderService();
  List<HealthcareProvider> _providers = [];
  bool _isLoading = true;
  String? _errorMessage;
  Position? _currentLocation;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadProviders();
  }

  Future<void> _loadProviders() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      // Get current location
      _currentLocation = await _healthcareService.getCurrentLocation();

      // Load providers based on risk level
      List<HealthcareProvider> providers;
      if (widget.riskLevel != null) {
        providers = await _healthcareService.getProvidersForRiskLevel(
          widget.riskLevel!,
        );
      } else {
        providers = await _healthcareService.findNearbyProviders();
      }

      if (mounted) {
        setState(() {
          _providers = providers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load healthcare providers: $e';
          _isLoading = false;
        });
      }
    }
  }

  List<HealthcareProvider> get _filteredProviders {
    if (_selectedFilter == 'all') return _providers;

    switch (_selectedFilter) {
      case 'hospitals':
        return _providers.where((p) => p.type == 'hospital').toList();
      case 'clinics':
        return _providers.where((p) => p.type == 'clinic').toList();
      case 'testing':
        return _providers.where((p) => p.hasHivTesting).toList();
      case 'treatment':
        return _providers.where((p) => p.hasHivTreatment).toList();
      case 'emergency':
        return _providers.where((p) => p.isEmergency).toList();
      default:
        return _providers;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.riskLevel != null
              ? '${widget.riskLevel} - Find Healthcare'
              : 'Find Healthcare Providers',
        ),
        backgroundColor: _getAppBarColor(),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          _buildRiskBanner(),
          _buildFilterChips(),
          Expanded(child: _buildProvidersList()),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _loadProviders,
        icon: const Icon(Icons.refresh),
        label: const Text('Refresh'),
        backgroundColor: _getAppBarColor(),
      ),
    );
  }

  Widget _buildRiskBanner() {
    if (widget.riskLevel == null) return const SizedBox.shrink();

    Color bannerColor;
    IconData bannerIcon;
    String bannerText;

    switch (widget.riskLevel!.toLowerCase()) {
      case 'high risk':
        bannerColor = Colors.red;
        bannerIcon = Icons.warning;
        bannerText = 'URGENT: Please visit a healthcare provider immediately';
        break;
      case 'medium risk':
        bannerColor = Colors.orange;
        bannerIcon = Icons.info;
        bannerText = 'RECOMMENDED: Schedule HIV testing within a few days';
        break;
      case 'low risk':
        bannerColor = Colors.green;
        bannerIcon = Icons.check_circle;
        bannerText = 'PREVENTIVE: Consider regular testing for peace of mind';
        break;
      default:
        bannerColor = Colors.blue;
        bannerIcon = Icons.info;
        bannerText = 'Find healthcare providers near you';
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bannerColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: bannerColor.withValues(alpha: 0.3)),
        ),
      ),
      child: Row(
        children: [
          Icon(bannerIcon, color: bannerColor, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              bannerText,
              style: TextStyle(
                color: bannerColor,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    final filters = [
      {'key': 'all', 'label': 'All', 'icon': Icons.list},
      {'key': 'hospitals', 'label': 'Hospitals', 'icon': Icons.local_hospital},
      {'key': 'clinics', 'label': 'Clinics', 'icon': Icons.medical_services},
      {'key': 'testing', 'label': 'HIV Testing', 'icon': Icons.science},
      {'key': 'treatment', 'label': 'Treatment', 'icon': Icons.medication},
      {'key': 'emergency', 'label': 'Emergency', 'icon': Icons.emergency},
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['key'];

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    filter['icon'] as IconData,
                    size: 16,
                    color: isSelected ? Colors.white : Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(filter['label'] as String),
                ],
              ),
              onSelected: (selected) {
                if (mounted) {
                  setState(() {
                    _selectedFilter = filter['key'] as String;
                  });
                }
              },
              selectedColor: _getAppBarColor(),
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProvidersList() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Finding healthcare providers near you...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadProviders,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final filteredProviders = _filteredProviders;

    if (filteredProviders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.location_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No healthcare providers found',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters or check your location settings',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredProviders.length,
      itemBuilder: (context, index) {
        return _buildProviderCard(filteredProviders[index]);
      },
    );
  }

  Widget _buildProviderCard(HealthcareProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(provider.typeIcon, style: const TextStyle(fontSize: 24)),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        provider.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        provider.typeDisplayName,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
                if (provider.distanceKm > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      provider.distanceText,
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    provider.address,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  provider.openingHours,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
            if (provider.availableServices.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: provider.availableServices.map((service) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Text(
                      service,
                      style: TextStyle(
                        color: Colors.green[700],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openDirections(provider),
                    icon: const Icon(Icons.directions),
                    label: const Text('Directions'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getAppBarColor(),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _callProvider(provider),
                    icon: const Icon(Icons.phone),
                    label: const Text('Call'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _getAppBarColor(),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getAppBarColor() {
    if (widget.riskLevel == null) return Colors.blue;

    switch (widget.riskLevel!.toLowerCase()) {
      case 'high risk':
        return Colors.red;
      case 'medium risk':
        return Colors.orange;
      case 'low risk':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  Future<void> _openDirections(HealthcareProvider provider) async {
    final url = _healthcareService.getDirectionsUrl(
      provider,
      fromLocation: _currentLocation,
    );

    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);

      // Show notification about the selected provider
      await NotificationService.showHospitalDirectionNotification(
        provider: provider,
        riskLevel: widget.riskLevel ?? 'general',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open directions: $e')),
        );
      }
    }
  }

  Future<void> _callProvider(HealthcareProvider provider) async {
    final url = _healthcareService.getCallUrl(provider);

    try {
      await launchUrl(Uri.parse(url));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Could not make call: $e')));
      }
    }
  }
}
