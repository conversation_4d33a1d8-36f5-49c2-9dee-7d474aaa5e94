import 'package:flutter/material.dart';
import '../services/language_service.dart';
import '../services/prediction_service.dart';
import '../services/auth_service.dart';
import 'home_screen.dart';
import 'beautiful_education_screen.dart';
import 'hospital_finder_screen.dart';
import 'settings_screen.dart';
import 'prediction_history_screen.dart';
import 'admin_dashboard_screen.dart';
import 'login_screen.dart';

class MainFeaturesScreen extends StatefulWidget {
  const MainFeaturesScreen({super.key});

  @override
  State<MainFeaturesScreen> createState() => _MainFeaturesScreenState();
}

class _MainFeaturesScreenState extends State<MainFeaturesScreen>
    with TickerProviderStateMixin {
  final LanguageService _languageService = LanguageService();
  final AuthService _authService = AuthService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _selectedLanguage = 'en';
  bool _isConnected = false;
  bool _isCheckingConnection = true;
  String _connectionMessage = '';
  String? _userName;
  bool _isAdmin = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserData();
    _checkApiConnection();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  Future<void> _loadUserData() async {
    final language = await _languageService.getCurrentLanguage();
    final user = await _authService.getCurrentUser();

    if (mounted) {
      setState(() {
        _selectedLanguage = language;
        if (user != null) {
          _userName = user.fullName.isNotEmpty ? user.fullName : user.username;
          _isAdmin = user.isAdmin;
        }
      });
    }
  }

  Future<void> _checkApiConnection() async {
    setState(() {
      _isCheckingConnection = true;
    });

    try {
      final status = await HivPredictionService.getConnectionStatus();
      if (mounted) {
        setState(() {
          _isConnected = status['connected'] ?? false;
          _connectionMessage = status['message'] ?? 'Unknown status';
          _isCheckingConnection = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isConnected = false;
          _connectionMessage = 'Connection check failed';
          _isCheckingConnection = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final texts = _languageService.getTexts(_selectedLanguage);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667eea), Color(0xFF764ba2), Color(0xFFf093fb)],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: CustomScrollView(
                slivers: [
                  // App Bar
                  SliverAppBar(
                    expandedHeight: 200,
                    floating: false,
                    pinned: true,
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    flexibleSpace: FlexibleSpaceBar(
                      title: Text(
                        texts['app_title'] ?? 'HIV Predictor',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      background: Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [Colors.transparent, Colors.black26],
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 40),
                            Container(
                              width: 80,
                              height: 80,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black26,
                                    blurRadius: 10,
                                    offset: Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.health_and_safety,
                                size: 40,
                                color: Color(0xFF667eea),
                              ),
                            ),
                            const SizedBox(height: 16),
                            if (_userName != null)
                              Text(
                                'Welcome, $_userName!',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    actions: [
                      // Language Selector
                      _buildLanguageSelector(),
                      // Connection Status
                      _buildConnectionIndicator(),
                      // Logout
                      IconButton(
                        icon: const Icon(Icons.logout, color: Colors.white),
                        onPressed: _signOut,
                      ),
                    ],
                  ),

                  // Main Content
                  SliverPadding(
                    padding: const EdgeInsets.all(16),
                    sliver: SliverList(
                      delegate: SliverChildListDelegate([
                        // Connection Status Card
                        if (!_isConnected) _buildConnectionStatusCard(),

                        // Quick Actions
                        _buildQuickActionsSection(texts),

                        const SizedBox(height: 24),

                        // Main Features Grid
                        _buildMainFeaturesGrid(texts),

                        const SizedBox(height: 24),

                        // Admin Features (if admin)
                        if (_isAdmin) _buildAdminSection(texts),

                        const SizedBox(height: 24),

                        // Footer
                        _buildFooter(texts),
                      ]),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
      ),
      child: DropdownButton<String>(
        value: _selectedLanguage,
        dropdownColor: const Color(0xFF667eea),
        underline: Container(),
        icon: const Icon(Icons.arrow_drop_down, color: Colors.white, size: 16),
        style: const TextStyle(color: Colors.white, fontSize: 12),
        items: const [
          DropdownMenuItem(value: 'en', child: Text('EN')),
          DropdownMenuItem(value: 'fr', child: Text('FR')),
          DropdownMenuItem(value: 'rw', child: Text('RW')),
        ],
        onChanged: (String? newValue) async {
          if (newValue != null) {
            await _languageService.setLanguage(newValue);
            setState(() {
              _selectedLanguage = newValue;
            });
          }
        },
      ),
    );
  }

  Widget _buildConnectionIndicator() {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: _isCheckingConnection
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Icon(
              _isConnected ? Icons.wifi : Icons.wifi_off,
              color: _isConnected ? Colors.green : Colors.red,
              size: 20,
            ),
    );
  }

  Widget _buildConnectionStatusCard() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.warning, color: Colors.red[700]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Connection Issue',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
                Text(
                  _connectionMessage,
                  style: TextStyle(color: Colors.red[600]),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: _checkApiConnection,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection(Map<String, String> texts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.add_circle,
                title: 'New Assessment',
                subtitle: 'Start HIV risk evaluation',
                color: const Color(0xFFE53935),
                onTap: () => _navigateToScreen(const HomeScreen()),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.local_hospital,
                title: 'Find Hospital',
                subtitle: 'Locate nearby healthcare',
                color: const Color(0xFF1976D2),
                onTap: () => _navigateToScreen(const HospitalFinderScreen()),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: Colors.white, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainFeaturesGrid(Map<String, String> texts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'All Features',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.1,
          children: [
            _buildFeatureCard(
              icon: Icons.psychology,
              title: texts['predict'] ?? 'Risk Assessment',
              subtitle: 'AI-powered HIV risk evaluation',
              color: const Color(0xFFE53935),
              onTap: () => _navigateToScreen(const HomeScreen()),
            ),
            _buildFeatureCard(
              icon: Icons.school,
              title: texts['education'] ?? 'Education',
              subtitle: 'Learn about HIV prevention',
              color: const Color(0xFF388E3C),
              onTap: () => _navigateToScreen(const BeautifulEducationScreen()),
            ),
            _buildFeatureCard(
              icon: Icons.local_hospital,
              title: 'Hospital Finder',
              subtitle: 'Find nearby healthcare',
              color: const Color(0xFF1976D2),
              onTap: () => _navigateToScreen(const HospitalFinderScreen()),
            ),
            _buildFeatureCard(
              icon: Icons.history,
              title: 'History',
              subtitle: 'View past assessments',
              color: const Color(0xFFF57C00),
              onTap: () => _navigateToScreen(const PredictionHistoryScreen()),
            ),
            _buildFeatureCard(
              icon: Icons.settings,
              title: texts['settings'] ?? 'Settings',
              subtitle: 'App preferences',
              color: const Color(0xFF7B1FA2),
              onTap: () => _navigateToScreen(const SettingsScreen()),
            ),
            _buildFeatureCard(
              icon: Icons.info,
              title: 'About',
              subtitle: 'App information',
              color: const Color(0xFF455A64),
              onTap: () => _showAboutDialog(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(icon, color: Colors.white, size: 28),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminSection(Map<String, String> texts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Admin Features',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        _buildFeatureCard(
          icon: Icons.admin_panel_settings,
          title: 'Admin Dashboard',
          subtitle: 'Manage users and view analytics',
          color: const Color(0xFF6A1B9A),
          onTap: () => _navigateToScreen(const AdminDashboardScreen()),
        ),
      ],
    );
  }

  Widget _buildFooter(Map<String, String> texts) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            texts['footer_text'] ?? 'Your health, our priority',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'HIV Predictor App v1.0.0',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _navigateToScreen(Widget screen) {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => screen));
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About HIV Predictor'),
        content: const Text(
          'HIV Predictor is an AI-powered mobile application designed to help users assess their HIV risk and find healthcare resources.\n\nVersion: 1.0.0\nDeveloped with Flutter',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _signOut() async {
    try {
      await _authService.signOut();
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error signing out: $e')));
      }
    }
  }
}
