import 'package:flutter/material.dart';
import '../services/language_service.dart';
import '../services/auth_service.dart';
import 'attractive_home_screen.dart';
import 'beautiful_education_screen.dart';
import 'improved_map_screen.dart';
import 'settings_screen.dart';
import 'dashboard_screen.dart';
import 'home_screen.dart';
import 'login_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  final LanguageService _languageService = LanguageService();
  final AuthService _authService = AuthService();
  String _selectedLanguage = 'en';
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await _loadLanguage();
    await _loadUserData();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadLanguage() async {
    final language = await _languageService.getCurrentLanguage();
    if (mounted) {
      setState(() {
        _selectedLanguage = language;
      });
    }
  }

  Future<void> _loadUserData() async {
    final user = await _authService.getCurrentUser();
    if (mounted) {
      setState(() {
        _currentUser = user;
      });
    }
  }

  List<Widget> get _screens {
    if (_currentUser != null) {
      // Authenticated user screens
      return [
        const AttractiveHomeScreen(), // Home - personalized dashboard
        const BeautifulEducationScreen(), // Education - always accessible
        const ImprovedMapScreen(), // Hospital finder - always accessible
        const HomeScreen(), // Risk assessment - full features
        const DashboardScreen(), // Personal dashboard - login required
        const SettingsScreen(), // Settings - login required
      ];
    } else {
      // Public user screens
      return [
        const AttractiveHomeScreen(), // Home - welcome + public features
        const BeautifulEducationScreen(), // Education - always accessible
        const ImprovedMapScreen(), // Hospital finder - always accessible
        const HomeScreen(), // Risk assessment - accessible but saves only if logged in
        const LoginScreen(), // Login/Signup - for authentication
      ];
    }
  }

  List<BottomNavigationBarItem> get _navItems {
    final texts = _languageService.getTexts(_selectedLanguage);

    if (_currentUser != null) {
      // Authenticated user navigation
      return [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home_rounded),
          label: texts['home'] ?? 'Home',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.school_rounded),
          label: texts['education'] ?? 'Education',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.location_on_rounded),
          label: texts['hospitals'] ?? 'Hospitals',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.psychology_rounded),
          label: texts['assessment'] ?? 'Assessment',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.dashboard_rounded),
          label: texts['dashboard'] ?? 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.settings_rounded),
          label: texts['settings'] ?? 'Settings',
        ),
      ];
    } else {
      // Public user navigation
      return [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home_rounded),
          label: texts['home'] ?? 'Home',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.school_rounded),
          label: texts['education'] ?? 'Education',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.location_on_rounded),
          label: texts['hospitals'] ?? 'Hospitals',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.psychology_rounded),
          label: texts['assessment'] ?? 'Assessment',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.login_rounded),
          label: texts['login'] ?? 'Login',
        ),
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: _screens),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  // Method to refresh authentication state (called when user logs in/out)
  Future<void> refreshAuthState() async {
    await _loadUserData();
    // Reset to home tab when auth state changes
    setState(() {
      _currentIndex = 0;
    });
  }

  Widget _buildBottomNavBar() {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: const Color(0xFF1976D2),
          unselectedItemColor: Colors.grey[600],
          selectedFontSize: 12,
          unselectedFontSize: 10,
          elevation: 0,
          items: _navItems,
        ),
      ),
    );
  }
}
