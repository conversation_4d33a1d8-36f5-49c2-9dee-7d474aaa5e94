import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:url_launcher/url_launcher.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  LatLng? _currentPosition;
  final MapController _mapController = MapController();
  final TextEditingController _searchController = TextEditingController();
  List<LatLng> _routePoints = [];
  Map<String, dynamic>? _nearestHospital;
  List<Map<String, dynamic>> _hospitals = [];
  bool _isLoading = false;
  bool _isNavigating = false;
  List<Map<String, dynamic>> _routeInstructions = [];
  double _routeDistance = 0.0;
  double _routeDuration = 0.0;
  int _currentInstructionIndex = 0;

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not launch phone call'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error making phone call'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showHospitalDetails(Map<String, dynamic> hospital) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with icon and name
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: hospital['type'] == 'hospital'
                                ? Colors.red.withValues(alpha: 0.1)
                                : Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            hospital['type'] == 'hospital'
                                ? Icons.local_hospital
                                : Icons.local_pharmacy,
                            color: hospital['type'] == 'hospital'
                                ? Colors.red
                                : Colors.blue,
                            size: 30,
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                hospital['name'],
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                hospital['type'].toString().toUpperCase(),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Information cards
                    if (hospital['address'] != null) ...[
                      _buildInfoCard(
                        icon: Icons.location_on,
                        title: 'Address',
                        content: hospital['address'],
                        iconColor: Colors.green,
                      ),
                      const SizedBox(height: 12),
                    ],

                    if (hospital['phone'] != null) ...[
                      _buildInfoCard(
                        icon: Icons.phone,
                        title: 'Phone',
                        content: hospital['phone'],
                        iconColor: Colors.blue,
                        onTap: () => _makePhoneCall(hospital['phone']),
                      ),
                      const SizedBox(height: 12),
                    ],

                    // Distance information
                    if (_currentPosition != null) ...[
                      _buildInfoCard(
                        icon: Icons.straighten,
                        title: 'Distance',
                        content:
                            '${_calculateDistance(hospital['location']).toStringAsFixed(1)} km away',
                        iconColor: Colors.orange,
                      ),
                      const SizedBox(height: 12),
                    ],

                    // Services (if available)
                    _buildInfoCard(
                      icon: Icons.medical_services,
                      title: 'Services',
                      content: hospital['type'] == 'hospital'
                          ? 'Emergency Care, General Medicine, Specialist Consultations, HIV Testing'
                          : 'Outpatient Care, Basic Medical Services, Consultations, HIV Testing',
                      iconColor: Colors.purple,
                    ),

                    const SizedBox(height: 12),

                    // Operating hours
                    _buildInfoCard(
                      icon: Icons.access_time,
                      title: 'Operating Hours',
                      content: hospital['type'] == 'hospital'
                          ? '24/7 Emergency Services'
                          : 'Mon-Fri: 8:00 AM - 6:00 PM\nSat: 9:00 AM - 2:00 PM\nSun: Emergency only',
                      iconColor: Colors.teal,
                    ),

                    const SizedBox(height: 30),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              _getDirectionsToHospital(hospital);
                            },
                            icon: const Icon(Icons.directions),
                            label: const Text('Get Directions'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                        if (hospital['phone'] != null) ...[
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                Navigator.pop(context);
                                _makePhoneCall(hospital['phone']);
                              },
                              icon: const Icon(Icons.phone),
                              label: const Text('Call Now'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String content,
    required Color iconColor,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: iconColor, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    content,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  void _getDirectionsToHospital(Map<String, dynamic> hospital) async {
    if (_currentPosition == null) return;

    setState(() {
      _isLoading = true;
      _nearestHospital = hospital;
    });

    try {
      await _calculateRoute(_currentPosition!, hospital['location']);
    } catch (e) {
      debugPrint('Error calculating route: $e');
      // Fallback to simple straight line
      setState(() {
        _routePoints = [_currentPosition!, hospital['location']];
        _isLoading = false;
      });
    }

    _mapController.fitCamera(
      CameraFit.bounds(
        bounds: LatLngBounds.fromPoints(_routePoints),
        padding: const EdgeInsets.all(50.0),
      ),
    );
  }

  Future<void> _calculateRoute(LatLng start, LatLng end) async {
    try {
      // Using OSRM (Open Source Routing Machine) for free routing
      final url = Uri.parse(
        'http://router.project-osrm.org/route/v1/driving/${start.longitude},${start.latitude};${end.longitude},${end.latitude}?overview=full&geometries=geojson&steps=true',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final route = data['routes'][0];
        final geometry = route['geometry']['coordinates'] as List;

        // Extract route points
        final routePoints = geometry
            .map((coord) => LatLng(coord[1].toDouble(), coord[0].toDouble()))
            .toList();

        // Extract basic route info
        final legs = route['legs'][0];
        final steps = legs['steps'] as List;
        final instructions = steps
            .map(
              (step) => {
                'instruction': step['maneuver']['instruction'] ?? 'Continue',
                'distance': step['distance']?.toDouble() ?? 0.0,
                'duration': step['duration']?.toDouble() ?? 0.0,
                'type': step['maneuver']['type'] ?? 'straight',
              },
            )
            .toList();

        setState(() {
          _routePoints = routePoints;
          _routeInstructions = instructions;
          _routeDistance =
              (route['distance'] ?? 0.0).toDouble() / 1000; // Convert to km
          _routeDuration =
              (route['duration'] ?? 0.0).toDouble() / 60; // Convert to minutes
          _isLoading = false;
        });
      } else {
        throw Exception('Failed to get route from OSRM');
      }
    } catch (e) {
      debugPrint('Error with routing: $e');
      // Final fallback - straight line
      setState(() {
        _routePoints = [start, end];
        _routeDistance = _calculateDistance(end);
        _routeDuration = _routeDistance * 2; // Rough estimate: 30 km/h average
        _isLoading = false;
      });
    }
  }

  double _calculateDistance(LatLng hospitalLocation) {
    if (_currentPosition == null) return 0.0;

    return Geolocator.distanceBetween(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
          hospitalLocation.latitude,
          hospitalLocation.longitude,
        ) /
        1000; // Convert to kilometers
  }

  void _startNavigation() {
    setState(() {
      _isNavigating = true;
      _currentInstructionIndex = 0;
    });

    // Show navigation bottom sheet
    _showNavigationSheet();
  }

  void _showNavigationSheet() {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Navigation Active',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: _stopNavigation,
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_routeInstructions.isNotEmpty &&
                _currentInstructionIndex < _routeInstructions.length) ...[
              Row(
                children: [
                  Icon(
                    _getInstructionIcon(
                      _routeInstructions[_currentInstructionIndex]['type'],
                    ),
                    size: 32,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _routeInstructions[_currentInstructionIndex]['instruction'],
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          'In ${(_routeInstructions[_currentInstructionIndex]['distance'] as double).toStringAsFixed(0)}m',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    Text(
                      '${_routeDistance.toStringAsFixed(1)} km',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text('Distance'),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      '${_routeDuration.toStringAsFixed(0)} min',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text('Duration'),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getInstructionIcon(String type) {
    switch (type.toLowerCase()) {
      case 'turn-left':
      case 'left':
        return Icons.turn_left;
      case 'turn-right':
      case 'right':
        return Icons.turn_right;
      case 'straight':
      case 'continue':
        return Icons.straight;
      case 'arrive':
      case 'destination':
        return Icons.location_on;
      default:
        return Icons.navigation;
    }
  }

  void _stopNavigation() {
    setState(() {
      _isNavigating = false;
      _currentInstructionIndex = 0;
    });
    Navigator.pop(context);
  }

  @override
  void initState() {
    super.initState();
    _determinePosition();
  }

  Future<void> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) return;

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) return;
    }

    if (permission == LocationPermission.deniedForever) return;

    Position position = await Geolocator.getCurrentPosition();
    if (mounted) {
      setState(() {
        _currentPosition = LatLng(position.latitude, position.longitude);
      });
    }

    // Fetch hospitals after getting current position
    await _fetchNearbyHospitals();
  }

  Future<void> _fetchNearbyHospitals() async {
    if (_currentPosition == null) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // Query radius in meters (5km)
      const radius = 5000;

      // Overpass API query to find hospitals and clinics
      final query =
          '''
        [out:json][timeout:25];
        (
          node["amenity"="hospital"](around:$radius,${_currentPosition!.latitude},${_currentPosition!.longitude});
          node["amenity"="clinic"](around:$radius,${_currentPosition!.latitude},${_currentPosition!.longitude});
          way["amenity"="hospital"](around:$radius,${_currentPosition!.latitude},${_currentPosition!.longitude});
          way["amenity"="clinic"](around:$radius,${_currentPosition!.latitude},${_currentPosition!.longitude});
        );
        out body;
        >;
        out skel qt;
      ''';

      final response = await http.post(
        Uri.parse('https://overpass-api.de/api/interpreter'),
        body: query,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final elements = data['elements'] as List;

        final hospitals = <Map<String, dynamic>>[];

        for (var element in elements) {
          if (element['type'] == 'node' && element['tags'] != null) {
            final tags = element['tags'];
            if (tags['amenity'] == 'hospital' || tags['amenity'] == 'clinic') {
              hospitals.add({
                'name': tags['name'] ?? 'Unnamed Hospital',
                'location': LatLng(element['lat'], element['lon']),
                'type': tags['amenity'],
                'phone': tags['phone'],
                'address': tags['addr:street'] != null
                    ? '${tags['addr:street']}, ${tags['addr:city'] ?? ''}'
                    : null,
              });
            }
          }
        }

        if (mounted) {
          setState(() {
            _hospitals = hospitals;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error fetching hospitals: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _findNearestHospital() {
    if (_currentPosition == null || _hospitals.isEmpty) return;

    double shortestDistance = double.infinity;
    Map<String, dynamic>? nearest;

    for (var hospital in _hospitals) {
      final distance = Geolocator.distanceBetween(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        hospital['location'].latitude,
        hospital['location'].longitude,
      );

      if (distance < shortestDistance) {
        shortestDistance = distance;
        nearest = hospital;
      }
    }

    if (nearest != null) {
      final nonNullNearest = nearest;
      if (mounted) {
        setState(() {
          _nearestHospital = nonNullNearest;
          _routePoints = [_currentPosition!, nonNullNearest['location']];
        });
      }

      _mapController.fitCamera(
        CameraFit.bounds(
          bounds: LatLngBounds.fromPoints(_routePoints),
          padding: const EdgeInsets.all(50.0),
        ),
      );
    }
  }

  void _searchHospitals(String query) {
    final results = _hospitals.where(
      (hospital) =>
          hospital['name'].toLowerCase().contains(query.toLowerCase()),
    );
    if (results.isNotEmpty) {
      final nearest = results.first;
      _mapController.move(nearest['location'], 15.0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Find Nearest Hospital'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchNearbyHospitals,
            tooltip: 'Refresh Location',
          ),
        ],
      ),
      body: _currentPosition == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search for hospitals...',
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.search),
                              onPressed: () {
                                _searchHospitals(_searchController.text);
                              },
                            ),
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.directions),
                        onPressed: _findNearestHospital,
                        tooltip: 'Find nearest hospital',
                      ),
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: _fetchNearbyHospitals,
                        tooltip: 'Refresh hospitals',
                      ),
                    ],
                  ),
                ),
                if (_isLoading)
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: LinearProgressIndicator(),
                  ),
                if (_nearestHospital != null)
                  Container(
                    margin: const EdgeInsets.all(8.0),
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.local_hospital, color: Colors.blue[700]),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _nearestHospital!['name'],
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            if (_routePoints.length >
                                2) // Only show if we have a real route
                              ElevatedButton.icon(
                                onPressed: _isNavigating
                                    ? _stopNavigation
                                    : _startNavigation,
                                icon: Icon(
                                  _isNavigating ? Icons.stop : Icons.navigation,
                                  size: 16,
                                ),
                                label: Text(
                                  _isNavigating ? 'Stop' : 'Navigate',
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _isNavigating
                                      ? Colors.red
                                      : Colors.green,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        if (_routeDistance > 0) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.straighten,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${_routeDistance.toStringAsFixed(1)} km',
                                style: TextStyle(color: Colors.grey[700]),
                              ),
                              const SizedBox(width: 16),
                              Icon(
                                Icons.access_time,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${_routeDuration.toStringAsFixed(0)} min',
                                style: TextStyle(color: Colors.grey[700]),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                Expanded(
                  child: FlutterMap(
                    mapController: _mapController,
                    options: MapOptions(
                      initialCenter: _currentPosition!,
                      initialZoom: 13.0,
                    ),
                    children: [
                      TileLayer(
                        urlTemplate:
                            'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                        subdomains: const ['a', 'b', 'c'],
                      ),
                      if (_routePoints.isNotEmpty)
                        PolylineLayer(
                          polylines: [
                            Polyline(
                              points: _routePoints,
                              color: Colors.blue,
                              strokeWidth: 3.0,
                            ),
                          ],
                        ),
                      MarkerLayer(
                        markers: [
                          if (_currentPosition != null)
                            Marker(
                              point: _currentPosition!,
                              width: 80.0,
                              height: 80.0,
                              child: const Icon(
                                Icons.location_pin,
                                color: Colors.red,
                                size: 40,
                              ),
                            ),
                          ..._hospitals.map(
                            (hospital) => Marker(
                              point: hospital['location'],
                              width: 60.0,
                              height: 60.0,
                              child: GestureDetector(
                                onTap: () => _showHospitalDetails(hospital),
                                child: Tooltip(
                                  message:
                                      '${hospital['name']}\n(${hospital['type']})',
                                  child: Icon(
                                    Icons.local_hospital,
                                    color: _nearestHospital == hospital
                                        ? Colors.green
                                        : Colors.blue,
                                    size: 35,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
}
