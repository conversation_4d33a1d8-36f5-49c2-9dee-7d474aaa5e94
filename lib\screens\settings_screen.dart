import 'package:flutter/material.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _locationEnabled = true;
  String _selectedTheme = 'system';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildNotificationSettings(),
          const SizedBox(height: 16),
          _buildPrivacySettings(),
          const SizedBox(height: 16),
          _buildAppearanceSettings(),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
    Color? color,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color ?? const Color(0xFF1976D2), size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color ?? const Color(0xFF1976D2),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return _buildSectionCard(
      title: 'Notifications',
      icon: Icons.notifications,
      color: Colors.orange,
      children: [
        SwitchListTile(
          title: const Text('Notifications'),
          subtitle: const Text('Receive app notifications'),
          value: _notificationsEnabled,
          onChanged: (value) {
            setState(() {
              _notificationsEnabled = value;
            });
            _showSnackBar('Notification settings updated', Colors.green);
          },
        ),
      ],
    );
  }

  Widget _buildPrivacySettings() {
    return _buildSectionCard(
      title: 'Privacy & Data',
      icon: Icons.privacy_tip,
      color: Colors.green,
      children: [
        SwitchListTile(
          title: const Text('Location Access'),
          subtitle: const Text('Allow location access for hospital finder'),
          value: _locationEnabled,
          onChanged: (value) {
            setState(() {
              _locationEnabled = value;
            });
            _showSnackBar('Location settings updated', Colors.green);
          },
        ),
      ],
    );
  }

  Widget _buildAppearanceSettings() {
    return _buildSectionCard(
      title: 'Appearance',
      icon: Icons.palette,
      color: Colors.purple,
      children: [
        ListTile(
          title: const Text('Theme'),
          subtitle: const Text('Choose app theme'),
          trailing: DropdownButton<String>(
            value: _selectedTheme,
            items: const [
              DropdownMenuItem(value: 'light', child: Text('Light')),
              DropdownMenuItem(value: 'dark', child: Text('Dark')),
              DropdownMenuItem(value: 'system', child: Text('System Default')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedTheme = value;
                });
                _showSnackBar('Theme updated successfully', Colors.green);
              }
            },
          ),
        ),
      ],
    );
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
