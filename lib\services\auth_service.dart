import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class User {
  final int id;
  final String username;
  final String email;
  final String fullName;
  final String? createdAt;
  final bool isAdmin;

  User({
    required this.id,
    required this.username,
    required this.email,
    required this.fullName,
    this.createdAt,
    this.isAdmin = false,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      fullName: json['full_name'] ?? json['fullName'],
      createdAt: json['created_at'],
      isAdmin: json['is_admin'] == 1 || json['isAdmin'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'full_name': fullName,
      'created_at': createdAt,
      'is_admin': isAdmin,
    };
  }
}

class AuthService {
  static const bool _demoMode =
      false; // Disable demo mode - require proper signup/signin
  static const List<String> _baseUrls = [
    'http://********:5001', // Android emulator
    'http://localhost:5001', // iOS simulator or web
    'http://127.0.0.1:5001', // Alternative localhost
  ];

  static const String _userKey = 'current_user';
  String? _workingBaseUrl;
  User? _currentUser;

  // Getter for current user
  User? get currentUser => _currentUser;

  Future<String> _getWorkingBaseUrl() async {
    if (_demoMode) {
      return _baseUrls.first;
    }

    // If we already found a working URL, use it
    if (_workingBaseUrl != null) {
      return _workingBaseUrl!;
    }

    // Try each URL until one works
    for (final url in _baseUrls) {
      try {
        final response = await http
            .get(Uri.parse('$url/api/health'))
            .timeout(const Duration(seconds: 2));

        if (response.statusCode == 200) {
          _workingBaseUrl = url;
          if (kDebugMode) {
            print('Found working API URL: $_workingBaseUrl');
          }
          return url;
        }
      } catch (_) {
        continue;
      }
    }

    // If all fail, return the first one and let the error be handled by the caller
    return _baseUrls.first;
  }

  Future<User?> getCurrentUser() async {
    if (_currentUser != null) {
      return _currentUser;
    }

    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);

    if (userJson == null) {
      return null;
    }

    try {
      _currentUser = User.fromJson(jsonDecode(userJson));
      return _currentUser;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing stored user: $e');
      }
      await prefs.remove(_userKey);
      return null;
    }
  }

  Future<void> saveUser(User user) async {
    _currentUser = user;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
  }

  Future<void> signOut() async {
    _currentUser = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
  }

  Future<Map<String, dynamic>> login(String username, String password) async {
    if (_demoMode) {
      // Demo mode: accept any non-empty username/password
      if (username.isNotEmpty && password.isNotEmpty) {
        final user = User(
          id: 1,
          username: username,
          email: '$<EMAIL>',
          fullName: username,
          isAdmin: username.toLowerCase() == 'admin',
        );
        await saveUser(user);
        return {
          'success': true,
          'user': user,
          'message': 'Demo login successful',
        };
      }
      return {
        'success': false,
        'message': 'Please enter both username and password',
      };
    }

    try {
      final baseUrl = await _getWorkingBaseUrl();

      if (kDebugMode) {
        print('Attempting login to $baseUrl/api/auth/signin');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/api/auth/signin'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'username': username, 'password': password}),
      );

      if (kDebugMode) {
        print('Login response status: ${response.statusCode}');
        print('Login response body: ${response.body}');
      }

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        final user = User.fromJson(data['user']);
        await saveUser(user);
        return {
          'success': true,
          'user': user,
          'message': data['message'] ?? 'Login successful',
        };
      } else {
        return {'success': false, 'message': data['message'] ?? 'Login failed'};
      }
    } catch (e) {
      if (kDebugMode) {
        print('Login error: $e');
      }
      return {'success': false, 'message': 'Connection error: $e'};
    }
  }

  Future<Map<String, dynamic>> register({
    required String username,
    required String password,
    required String email,
    required String fullName,
  }) async {
    if (_demoMode) {
      // Demo mode: accept any registration with valid fields
      if (username.isNotEmpty &&
          password.isNotEmpty &&
          email.isNotEmpty &&
          fullName.isNotEmpty) {
        final user = User(
          id: DateTime.now().millisecondsSinceEpoch,
          username: username,
          email: email,
          fullName: fullName,
        );
        await saveUser(user);
        return {
          'success': true,
          'user': user,
          'message': 'Demo registration successful',
        };
      }
      return {'success': false, 'message': 'Please fill in all fields'};
    }

    try {
      final baseUrl = await _getWorkingBaseUrl();

      if (kDebugMode) {
        print('Attempting registration to $baseUrl/api/auth/register');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/api/auth/register'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'password': password,
          'email': email,
          'full_name': fullName,
        }),
      );

      if (kDebugMode) {
        print('Register response status: ${response.statusCode}');
        print('Register response body: ${response.body}');
      }

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        final user = User.fromJson(data['user']);
        await saveUser(user);
        return {
          'success': true,
          'user': user,
          'message': data['message'] ?? 'Registration successful',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Registration failed',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Registration error: $e');
      }
      return {'success': false, 'message': 'Connection error: $e'};
    }
  }
}
