import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'prediction_service.dart';

class ConnectionManager {
  static final ConnectionManager _instance = ConnectionManager._internal();
  factory ConnectionManager() => _instance;
  ConnectionManager._internal();

  Timer? _reconnectionTimer;
  bool _isConnected = false;
  bool _isReconnecting = false;
  int _reconnectionAttempts = 0;
  static const int _maxReconnectionAttempts = 10;
  static const Duration _reconnectionInterval = Duration(seconds: 15);

  // Stream controller for connection status updates
  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();

  /// Stream of connection status changes
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;

  /// Current connection status
  bool get isConnected => _isConnected;

  /// Start automatic connection monitoring and reconnection
  void startConnectionManager() {
    if (kDebugMode) {
      print('🔄 Starting Connection Manager...');
    }

    // Initial connection check
    _checkConnection();

    // Start periodic reconnection attempts
    _reconnectionTimer = Timer.periodic(_reconnectionInterval, (timer) {
      if (!_isConnected && !_isReconnecting) {
        _attemptReconnection();
      }
    });
  }

  /// Stop the connection manager
  void stopConnectionManager() {
    _reconnectionTimer?.cancel();
    _reconnectionTimer = null;
    _isReconnecting = false;

    if (kDebugMode) {
      print('⏹️ Connection Manager stopped');
    }
  }

  /// Check current connection status
  Future<void> _checkConnection() async {
    try {
      final status = await HivPredictionService.getConnectionStatus();
      final connected = status['connected'] ?? false;

      if (connected != _isConnected) {
        _isConnected = connected;
        _connectionStatusController.add(_isConnected);

        if (connected) {
          _reconnectionAttempts = 0;
          if (kDebugMode) {
            print('✅ Connection restored');
          }
        } else {
          if (kDebugMode) {
            print('❌ Connection lost');
          }
        }
      }
    } catch (e) {
      if (_isConnected) {
        _isConnected = false;
        _connectionStatusController.add(_isConnected);
        if (kDebugMode) {
          print('❌ Connection check failed: $e');
        }
      }
    }
  }

  /// Attempt to reconnect to the API
  Future<void> _attemptReconnection() async {
    if (_isReconnecting || _reconnectionAttempts >= _maxReconnectionAttempts) {
      return;
    }

    _isReconnecting = true;
    _reconnectionAttempts++;

    if (kDebugMode) {
      print(
        '🔄 Reconnection attempt $_reconnectionAttempts/$_maxReconnectionAttempts',
      );
    }

    try {
      final connected = await HivPredictionService.ensureConnection(
        maxRetries: 3,
      );

      if (connected) {
        _isConnected = true;
        _reconnectionAttempts = 0;
        _connectionStatusController.add(_isConnected);

        if (kDebugMode) {
          print('✅ Reconnection successful!');
        }
      } else {
        if (kDebugMode) {
          print('❌ Reconnection attempt $_reconnectionAttempts failed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Reconnection error: $e');
      }
    } finally {
      _isReconnecting = false;
    }
  }

  /// Force an immediate connection attempt
  Future<bool> forceReconnection() async {
    if (kDebugMode) {
      print('🔄 Force reconnection requested...');
    }

    _reconnectionAttempts = 0; // Reset attempts for manual retry
    await _attemptReconnection();
    return _isConnected;
  }

  /// Get detailed connection status
  Future<Map<String, dynamic>> getDetailedStatus() async {
    try {
      final status = await HivPredictionService.getConnectionStatus();
      return {
        ...status,
        'reconnection_attempts': _reconnectionAttempts,
        'is_reconnecting': _isReconnecting,
        'manager_active': _reconnectionTimer?.isActive ?? false,
      };
    } catch (e) {
      return {
        'connected': false,
        'message': 'Status check failed: $e',
        'reconnection_attempts': _reconnectionAttempts,
        'is_reconnecting': _isReconnecting,
        'manager_active': _reconnectionTimer?.isActive ?? false,
      };
    }
  }

  /// Reset connection manager state
  void reset() {
    _reconnectionAttempts = 0;
    _isReconnecting = false;
    HivPredictionService.refreshUrlCache();

    if (kDebugMode) {
      print('🔄 Connection Manager reset');
    }
  }

  /// Dispose resources
  void dispose() {
    stopConnectionManager();
    _connectionStatusController.close();
  }
}

/// Widget that provides connection status to its children
class ConnectionProvider extends StatefulWidget {
  final Widget child;

  const ConnectionProvider({super.key, required this.child});

  @override
  State<ConnectionProvider> createState() => _ConnectionProviderState();
}

class _ConnectionProviderState extends State<ConnectionProvider> {
  final ConnectionManager _connectionManager = ConnectionManager();
  late StreamSubscription<bool> _connectionSubscription;

  @override
  void initState() {
    super.initState();

    // Start connection manager
    _connectionManager.startConnectionManager();

    // Listen to connection status changes
    _connectionSubscription = _connectionManager.connectionStatusStream.listen((
      connected,
    ) {
      // Connection status is managed globally, no need to update local state
    });
  }

  @override
  void dispose() {
    _connectionSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Mixin for widgets that need connection status
mixin ConnectionAware<T extends StatefulWidget> on State<T> {
  final ConnectionManager _connectionManager = ConnectionManager();
  late StreamSubscription<bool> _connectionSubscription;
  bool isConnected = false;

  @override
  void initState() {
    super.initState();

    // Listen to connection status changes
    _connectionSubscription = _connectionManager.connectionStatusStream.listen((
      connected,
    ) {
      if (mounted) {
        setState(() {
          isConnected = connected;
        });
        onConnectionChanged(connected);
      }
    });

    // Initial status
    isConnected = _connectionManager.isConnected;
  }

  @override
  void dispose() {
    _connectionSubscription.cancel();
    super.dispose();
  }

  /// Override this method to handle connection status changes
  void onConnectionChanged(bool connected) {
    // Default implementation - can be overridden
  }

  /// Force a reconnection attempt
  Future<bool> forceReconnection() {
    return _connectionManager.forceReconnection();
  }

  /// Get detailed connection status
  Future<Map<String, dynamic>> getConnectionStatus() {
    return _connectionManager.getDetailedStatus();
  }
}
