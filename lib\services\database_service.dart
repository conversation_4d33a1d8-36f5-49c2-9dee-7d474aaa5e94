// Temporarily disabled old database service - will be replaced with Firebase
/*
import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/user.dart';
import '../models/prediction_result.dart';

class DatabaseService {
  // Singleton pattern
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  // API configuration
  static const String _baseUrl = 'http://localhost:5001';
  bool _initialized = false;

  Future<void> get database async {
    if (!_initialized) {
      await _initDatabase();
    }
  }

  Future<void> _initDatabase() async {
    try {
      // Test API connection
      final response = await http.get(Uri.parse('$_baseUrl/health'));

      if (response.statusCode == 200) {
        _initialized = true;
        if (kDebugMode) {
          print('Connected to Flask API at $_baseUrl');
        }
      } else {
        throw Exception('API not responding');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to connect to Flask API: $e');
        print('Make sure Flask server is running on port 5001');
      }
      rethrow;
    }
  }

  // User operations
  Future<User?> createUser(User user) async {
    try {
      await database; // Ensure initialized

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/signup'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': user.username,
          'password': user.password,
          'email': user.email,
          'full_name': user.fullName,
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          final userData = data['user'];
          return User(
            id: userData['id'],
            username: userData['username'],
            password: user.password, // Keep original password
            email: userData['email'],
            fullName: userData['full_name'],
            createdAt: DateTime.parse(userData['created_at']),
            isAdmin: userData['is_admin'],
          );
        }
      } else {
        final error = jsonDecode(response.body);
        if (kDebugMode) {
          print('Error creating user: ${error['error']}');
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating user: $e');
      }
      return null;
    }
  }

  Future<User?> getUserByUsername(String username) async {
    try {
      await database; // Ensure initialized

      // Get all users and find by username
      final users = await getAllUsers();
      return users.where((u) => u.username == username).firstOrNull;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user: $e');
      }
      return null;
    }
  }

  Future<User?> authenticateUser(String username, String password) async {
    try {
      await database; // Ensure initialized

      final response = await http
          .post(
            Uri.parse('$_baseUrl/auth/signin'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'username': username, 'password': password}),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          final userData = data['user'];
          return User(
            id: userData['id'],
            username: userData['username'],
            password: password, // Keep original password
            email: userData['email'],
            fullName: userData['full_name'],
            createdAt: DateTime.parse(userData['created_at']),
            isAdmin: userData['is_admin'],
          );
        }
      } else if (response.statusCode == 401) {
        // Invalid credentials
        return null;
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error authenticating user: $e');
      }
      // Throw exception to let AuthService handle fallback
      throw Exception('API not responding');
    }
  }

  Future<List<User>> getAllUsers() async {
    try {
      await database; // Ensure initialized

      final response = await http.get(Uri.parse('$_baseUrl/users'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          final usersData = data['users'] as List;
          return usersData
              .map(
                (userData) => User(
                  id: userData['id'],
                  username: userData['username'],
                  password: '', // Don't expose password
                  email: userData['email'],
                  fullName: userData['full_name'],
                  createdAt: DateTime.parse(userData['created_at']),
                  isAdmin: userData['is_admin'],
                ),
              )
              .toList();
        }
      }
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all users: $e');
      }
      return [];
    }
  }

  // Prediction operations
  Future<PredictionResult?> savePredictionResult(
    PredictionResult result,
  ) async {
    try {
      await database; // Ensure initialized

      final response = await http.post(
        Uri.parse('$_baseUrl/predictions'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'user_id': result.userId,
          'prediction': result.prediction,
          'confidence': result.confidence,
          'symptoms': result.symptoms,
          'exposure_locations': result.exposureLocations,
          'location_warning': result.locationWarning,
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          if (kDebugMode) {
            print('Prediction saved for user ${result.userId}');
          }
          return result; // Return the original result
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving prediction result: $e');
      }
      return null;
    }
  }

  Future<List<PredictionResult>> getUserPredictions(int userId) async {
    try {
      await database; // Ensure initialized

      final response = await http.get(
        Uri.parse('$_baseUrl/predictions/user/$userId'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          final predictionsData = data['predictions'] as List;
          return predictionsData
              .map(
                (predData) => PredictionResult(
                  id: predData['id'],
                  userId: predData['user_id'],
                  prediction: predData['prediction'],
                  confidence: predData['confidence'].toDouble(),
                  symptoms: Map<String, int>.from(predData['symptoms']),
                  exposureLocations: predData['exposure_locations'] != null
                      ? Map<String, String>.from(predData['exposure_locations'])
                      : null,
                  locationWarning: predData['location_warning'] != null
                      ? Map<String, dynamic>.from(predData['location_warning'])
                      : null,
                  createdAt: DateTime.parse(predData['created_at']),
                ),
              )
              .toList();
        }
      }
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user predictions: $e');
      }
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getAllPredictionsWithUsers() async {
    try {
      await database; // Ensure initialized

      final response = await http.get(Uri.parse('$_baseUrl/predictions/all'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          final predictionsData = data['predictions'] as List;
          return predictionsData
              .map(
                (predData) => {
                  'id': predData['id'],
                  'user_id': predData['user_id'],
                  'prediction': predData['prediction'],
                  'confidence': predData['confidence'],
                  'symptoms': predData['symptoms'].toString(),
                  'exposure_locations': predData['exposure_locations']
                      ?.toString(),
                  'location_warning': predData['location_warning']?.toString(),
                  'created_at': predData['created_at'],
                  'username': predData['username'],
                  'full_name': predData['full_name'],
                  'email': predData['email'],
                },
              )
              .cast<Map<String, dynamic>>()
              .toList();
        }
      }
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all predictions with users: $e');
      }
      return [];
    }
  }

  // Utility methods
  Future<void> closeDatabase() async {
    _initialized = false;
  }

  Future<void> deleteDatabase() async {
    // For API-based service, this would typically be an admin operation
    // Not implemented for safety
    if (kDebugMode) {
      print('Database deletion not implemented for API service');
    }
  }
}
*/

// Temporary placeholder class to avoid import errors
import '../models/prediction_result.dart';
import '../models/user.dart';

class DatabaseService {
  // This will be replaced with Firebase services

  Future<List<PredictionResult>> getUserPredictions(String userId) async {
    return [];
  }

  Future<List<User>> getAllUsers() async {
    return [];
  }

  Future<List<Map<String, dynamic>>> getAllPredictionsWithUsers() async {
    return [];
  }
}
