import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';
import '../models/user.dart' as app_user;

class FirebaseAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Convert Firebase User to App User
  app_user.User? _userFromFirebaseUser(User? user) {
    if (user == null) return null;

    return app_user.User(
      id: user.uid,
      email: user.email ?? '',
      fullName: user.displayName ?? 'User',
      createdAt: user.metadata.creationTime ?? DateTime.now(),
    );
  }

  // Get current app user
  Future<app_user.User?> getCurrentUser() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      // Get additional user data from Firestore
      final doc = await _firestore.collection('users').doc(user.uid).get();
      if (doc.exists) {
        final data = doc.data()!;
        return app_user.User(
          id: user.uid,
          email: user.email ?? '',
          fullName: data['fullName'] ?? user.displayName ?? 'User',
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ??
              user.metadata.creationTime ??
              DateTime.now(),
        );
      }
    } catch (e) {
      _logger.e('Error getting user data: $e');
    }

    return _userFromFirebaseUser(user);
  }

  // Sign up with email and password
  Future<app_user.User?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      // Create user account with timeout
      final credential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password)
          .timeout(const Duration(seconds: 15));

      final user = credential.user;
      if (user != null) {
        // Perform operations in parallel for better performance
        await Future.wait([
          // Update display name
          user.updateDisplayName(fullName).timeout(const Duration(seconds: 10)),

          // Save additional user data to Firestore
          _firestore
              .collection('users')
              .doc(user.uid)
              .set({
                'email': email,
                'fullName': fullName,
                'createdAt': FieldValue.serverTimestamp(),
                'lastLoginAt': FieldValue.serverTimestamp(),
              })
              .timeout(const Duration(seconds: 10)),
        ]);

        _logger.i('User account created successfully: ${user.uid}');

        return app_user.User(
          id: user.uid,
          email: email,
          fullName: fullName,
          createdAt: DateTime.now(),
        );
      }
    } catch (e) {
      _logger.e('Sign up error: $e');

      // Provide more specific error messages
      if (e.toString().contains('email-already-in-use')) {
        throw Exception(
          'This email address is already registered. Please use a different email or try signing in.',
        );
      } else if (e.toString().contains('weak-password')) {
        throw Exception(
          'Password is too weak. Please use at least 6 characters with a mix of letters and numbers.',
        );
      } else if (e.toString().contains('invalid-email')) {
        throw Exception('Please enter a valid email address.');
      } else if (e.toString().contains('TimeoutException')) {
        throw Exception(
          'Registration is taking longer than expected. Please check your internet connection and try again.',
        );
      }

      rethrow;
    }
    return null;
  }

  // Sign in with email and password
  Future<app_user.User?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = credential.user;
      if (user != null) {
        // Update last login time
        await _firestore.collection('users').doc(user.uid).update({
          'lastLoginAt': FieldValue.serverTimestamp(),
        });

        return await getCurrentUser();
      }
    } catch (e) {
      _logger.e('Sign in error: $e');
      rethrow;
    }
    return null;
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      _logger.e('Sign out error: $e');
      rethrow;
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      _logger.e('Reset password error: $e');
      rethrow;
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user != null) {
        // Delete user data from Firestore
        await _firestore.collection('users').doc(user.uid).delete();

        // Delete user predictions
        final predictions = await _firestore
            .collection('predictions')
            .where('userId', isEqualTo: user.uid)
            .get();

        for (final doc in predictions.docs) {
          await doc.reference.delete();
        }

        // Delete Firebase Auth account
        await user.delete();
      }
    } catch (e) {
      _logger.e('Delete account error: $e');
      rethrow;
    }
  }

  // Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      _logger.i('Password reset email sent to: $email');
    } on FirebaseAuthException catch (e) {
      _logger.e('Error sending password reset email: ${e.code} - ${e.message}');

      switch (e.code) {
        case 'user-not-found':
          throw 'No account found with this email address. Please check your email or sign up for a new account.';
        case 'invalid-email':
          throw 'Please enter a valid email address.';
        case 'too-many-requests':
          throw 'Too many password reset attempts. Please try again later.';
        default:
          throw 'Failed to send password reset email. Please try again.';
      }
    } catch (e) {
      _logger.e('Unexpected error sending password reset email: $e');
      throw 'An unexpected error occurred. Please try again.';
    }
  }

  // Check if email exists
  Future<bool> isEmailRegistered(String email) async {
    try {
      // Since fetchSignInMethodsForEmail is deprecated, we'll use a different approach
      // Try to sign in with a dummy password to check if email exists
      await _auth.signInWithEmailAndPassword(
        email: email,
        password: 'dummy_password_check',
      );
      // If we reach here without exception, email exists but password was wrong
      return true;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        return false; // Email is not registered
      } else if (e.code == 'wrong-password' || e.code == 'invalid-credential') {
        return true; // Email exists but password is wrong
      }
      _logger.e('Check email error: $e');
      return false;
    } catch (e) {
      _logger.e('Check email error: $e');
      return false;
    }
  }
}
