import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import '../models/prediction_result.dart';
import '../models/user.dart' as app_user;

class FirebaseDatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Logger _logger = Logger();

  // Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  // Save prediction result
  Future<String> savePredictionResult(PredictionResult prediction) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final docRef = await _firestore.collection('predictions').add({
        'userId': userId,
        'prediction': prediction.prediction,
        'confidence': prediction.confidence,
        'symptoms': prediction.symptoms,
        'riskLevel': prediction.riskLevel,
        'recommendations': prediction.recommendations,
        'exposureLocations': prediction.exposureLocations,
        'locationWarning': prediction.locationWarning,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return docRef.id;
    } catch (e) {
      _logger.e('Error saving prediction: $e');
      rethrow;
    }
  }

  // Get user's prediction history
  Future<List<PredictionResult>> getUserPredictions() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection('predictions')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        return PredictionResult(
          id: doc.id,
          userId: data['userId'],
          prediction: data['prediction'] ?? '',
          confidence: (data['confidence'] ?? 0.0).toDouble(),
          symptoms: Map<String, dynamic>.from(data['symptoms'] ?? {}),
          riskLevel: data['riskLevel'] ?? 'Unknown',
          recommendations: List<String>.from(data['recommendations'] ?? []),
          exposureLocations: data['exposureLocations'] != null
              ? Map<String, String>.from(data['exposureLocations'])
              : null,
          locationWarning: data['locationWarning'] != null
              ? Map<String, dynamic>.from(data['locationWarning'])
              : null,
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        );
      }).toList();
    } catch (e) {
      _logger.e('Error getting predictions: $e');
      return [];
    }
  }

  // Get recent predictions (limited)
  Future<List<PredictionResult>> getRecentPredictions({int limit = 5}) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection('predictions')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        return PredictionResult(
          id: doc.id,
          userId: data['userId'],
          prediction: data['prediction'] ?? '',
          confidence: (data['confidence'] ?? 0.0).toDouble(),
          symptoms: Map<String, dynamic>.from(data['symptoms'] ?? {}),
          riskLevel: data['riskLevel'] ?? 'Unknown',
          recommendations: List<String>.from(data['recommendations'] ?? []),
          exposureLocations: data['exposureLocations'] != null
              ? Map<String, String>.from(data['exposureLocations'])
              : null,
          locationWarning: data['locationWarning'] != null
              ? Map<String, dynamic>.from(data['locationWarning'])
              : null,
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        );
      }).toList();
    } catch (e) {
      _logger.e('Error getting recent predictions: $e');
      return [];
    }
  }

  // Delete prediction
  Future<void> deletePrediction(String predictionId) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Verify the prediction belongs to the current user
      final doc = await _firestore
          .collection('predictions')
          .doc(predictionId)
          .get();
      if (!doc.exists) {
        throw Exception('Prediction not found');
      }

      final data = doc.data()!;
      if (data['userId'] != userId) {
        throw Exception('Unauthorized access');
      }

      await _firestore.collection('predictions').doc(predictionId).delete();
    } catch (e) {
      _logger.e('Error deleting prediction: $e');
      rethrow;
    }
  }

  // Get prediction statistics (for individual users)
  Future<Map<String, dynamic>> getPredictionStats() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        return {
          'totalPredictions': 0,
          'riskDistribution': <String, int>{},
          'lastPredictionDate': null,
        };
      }

      final querySnapshot = await _firestore
          .collection('predictions')
          .where('userId', isEqualTo: userId)
          .get();

      final predictions = querySnapshot.docs;
      final riskDistribution = <String, int>{};
      DateTime? lastPredictionDate;

      for (final doc in predictions) {
        final data = doc.data();
        final riskLevel = data['riskLevel'] ?? 'Unknown';
        riskDistribution[riskLevel] = (riskDistribution[riskLevel] ?? 0) + 1;

        final createdAt = (data['createdAt'] as Timestamp?)?.toDate();
        if (createdAt != null) {
          if (lastPredictionDate == null ||
              createdAt.isAfter(lastPredictionDate)) {
            lastPredictionDate = createdAt;
          }
        }
      }

      return {
        'totalPredictions': predictions.length,
        'riskDistribution': riskDistribution,
        'lastPredictionDate': lastPredictionDate,
      };
    } catch (e) {
      _logger.e('Error getting prediction stats: $e');
      return {
        'totalPredictions': 0,
        'riskDistribution': <String, int>{},
        'lastPredictionDate': null,
      };
    }
  }

  // ADMIN METHODS - Get comprehensive statistics for all users
  Future<Map<String, dynamic>> getAdminPredictionStats() async {
    try {
      // Get all predictions
      final allPredictionsSnapshot = await _firestore
          .collection('predictions')
          .get();

      // Get all users
      final allUsersSnapshot = await _firestore.collection('users').get();

      final predictions = allPredictionsSnapshot.docs;
      final totalUsers = allUsersSnapshot.docs.length;
      final totalPredictions = predictions.length;

      // Risk level distribution
      int highRisk = 0;
      int mediumRisk = 0;
      int lowRisk = 0;

      // Time-based statistics
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final thisWeek = now.subtract(Duration(days: now.weekday - 1));
      final thisMonth = DateTime(now.year, now.month, 1);

      int todayPredictions = 0;
      int thisWeekPredictions = 0;
      int thisMonthPredictions = 0;

      // User activity tracking
      final Map<String, int> userPredictionCounts = {};
      final Map<String, DateTime> lastUserActivity = {};

      for (final doc in predictions) {
        final data = doc.data();
        final prediction = data['prediction']?.toString().toLowerCase() ?? '';
        final riskLevel = data['riskLevel']?.toString().toLowerCase() ?? '';
        final userId = data['userId'] as String?;
        final createdAt = (data['createdAt'] as Timestamp?)?.toDate();

        // Count risk levels
        if (prediction.contains('high') || riskLevel.contains('high')) {
          highRisk++;
        } else if (prediction.contains('medium') ||
            riskLevel.contains('medium')) {
          mediumRisk++;
        } else if (prediction.contains('low') || riskLevel.contains('low')) {
          lowRisk++;
        }

        // Time-based counts
        if (createdAt != null) {
          if (createdAt.isAfter(today)) {
            todayPredictions++;
          }
          if (createdAt.isAfter(thisWeek)) {
            thisWeekPredictions++;
          }
          if (createdAt.isAfter(thisMonth)) {
            thisMonthPredictions++;
          }

          // User activity tracking
          if (userId != null) {
            userPredictionCounts[userId] =
                (userPredictionCounts[userId] ?? 0) + 1;
            if (lastUserActivity[userId] == null ||
                createdAt.isAfter(lastUserActivity[userId]!)) {
              lastUserActivity[userId] = createdAt;
            }
          }
        }
      }

      // Calculate additional metrics
      final activeUsersToday = predictions
          .where((doc) {
            final createdAt = (doc.data()['createdAt'] as Timestamp?)?.toDate();
            return createdAt != null && createdAt.isAfter(today);
          })
          .map((doc) => doc.data()['userId'])
          .toSet()
          .length;

      final averagePredictionsPerUser = totalUsers > 0
          ? totalPredictions / totalUsers
          : 0.0;

      return {
        'success': true,
        'stats': {
          // Basic counts
          'total_users': totalUsers,
          'total_predictions': totalPredictions,
          'high_risk_predictions': highRisk,
          'medium_risk_predictions': mediumRisk,
          'low_risk_predictions': lowRisk,

          // Time-based statistics
          'today_predictions': todayPredictions,
          'this_week_predictions': thisWeekPredictions,
          'this_month_predictions': thisMonthPredictions,

          // User engagement metrics
          'active_users_today': activeUsersToday,
          'average_predictions_per_user': averagePredictionsPerUser
              .toStringAsFixed(2),

          // Risk distribution percentages
          'high_risk_percentage': totalPredictions > 0
              ? (highRisk / totalPredictions * 100).toStringAsFixed(1)
              : '0.0',
          'medium_risk_percentage': totalPredictions > 0
              ? (mediumRisk / totalPredictions * 100).toStringAsFixed(1)
              : '0.0',
          'low_risk_percentage': totalPredictions > 0
              ? (lowRisk / totalPredictions * 100).toStringAsFixed(1)
              : '0.0',

          // Additional insights
          'most_active_users': userPredictionCounts.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value))
            ..take(5)
                .map((e) => {'userId': e.key, 'predictionCount': e.value})
                .toList(),
        },
      };
    } catch (e) {
      _logger.e('Error getting admin prediction stats: $e');
      return {
        'success': false,
        'error': e.toString(),
        'stats': {
          'total_users': 0,
          'total_predictions': 0,
          'high_risk_predictions': 0,
          'medium_risk_predictions': 0,
          'low_risk_predictions': 0,
          'today_predictions': 0,
        },
      };
    }
  }

  // ADMIN METHODS - Get all predictions with user details (like Flask /api/predictions/all)
  Future<List<Map<String, dynamic>>> getAllPredictionsWithUsers() async {
    try {
      // Get all predictions
      final predictionsSnapshot = await _firestore
          .collection('predictions')
          .orderBy('createdAt', descending: true)
          .get();

      // Get all users for lookup
      final usersSnapshot = await _firestore.collection('users').get();
      final userMap = <String, Map<String, dynamic>>{};

      for (final userDoc in usersSnapshot.docs) {
        userMap[userDoc.id] = userDoc.data();
      }

      final result = <Map<String, dynamic>>[];

      for (final predDoc in predictionsSnapshot.docs) {
        final predData = predDoc.data();
        final userId = predData['userId'] as String?;
        final userData = userId != null ? userMap[userId] : null;

        result.add({
          'id': predDoc.id,
          'user_id': userId,
          'prediction': predData['prediction'] ?? '',
          'confidence': predData['confidence'] ?? 0.0,
          'symptoms': predData['symptoms']?.toString() ?? '{}',
          'exposure_locations': predData['exposureLocations']?.toString(),
          'location_warning': predData['locationWarning']?.toString(),
          'created_at':
              (predData['createdAt'] as Timestamp?)
                  ?.toDate()
                  .toIso8601String() ??
              '',
          'username': userData?['username'] ?? 'Unknown',
          'full_name': userData?['fullName'] ?? 'Unknown User',
          'email': userData?['email'] ?? '<EMAIL>',
          'risk_level': predData['riskLevel'] ?? 'Unknown',
          'recommendations': predData['recommendations']?.toString() ?? '[]',
        });
      }

      return result;
    } catch (e) {
      _logger.e('Error getting all predictions with users: $e');
      return [];
    }
  }

  // ADMIN METHODS - Get all users (like Flask /api/users)
  Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final usersSnapshot = await _firestore
          .collection('users')
          .orderBy('createdAt', descending: true)
          .get();

      final result = <Map<String, dynamic>>[];

      for (final userDoc in usersSnapshot.docs) {
        final userData = userDoc.data();

        // Get prediction count for this user
        final userPredictionsSnapshot = await _firestore
            .collection('predictions')
            .where('userId', isEqualTo: userDoc.id)
            .get();

        result.add({
          'id': userDoc.id,
          'username': userData['username'] ?? '',
          'email': userData['email'] ?? '',
          'full_name': userData['fullName'] ?? '',
          'created_at':
              (userData['createdAt'] as Timestamp?)
                  ?.toDate()
                  .toIso8601String() ??
              '',
          'last_login_at':
              (userData['lastLoginAt'] as Timestamp?)
                  ?.toDate()
                  .toIso8601String() ??
              '',
          'is_admin': userData['isAdmin'] ?? false,
          'prediction_count': userPredictionsSnapshot.docs.length,
        });
      }

      return result;
    } catch (e) {
      _logger.e('Error getting all users: $e');
      return [];
    }
  }

  // ADMIN METHODS - Get predictions by risk level (enhanced filtering)
  Future<List<Map<String, dynamic>>> getPredictionsByRiskLevel(
    String riskLevel,
  ) async {
    try {
      Query query = _firestore.collection('predictions');

      if (riskLevel.toLowerCase() != 'all') {
        query = query.where('riskLevel', isEqualTo: riskLevel);
      }

      final predictionsSnapshot = await query
          .orderBy('createdAt', descending: true)
          .get();

      // Get user data for each prediction
      final result = <Map<String, dynamic>>[];

      for (final predDoc in predictionsSnapshot.docs) {
        final predData = predDoc.data() as Map<String, dynamic>;
        final userId = predData['userId'] as String?;

        Map<String, dynamic>? userData;
        if (userId != null) {
          final userDoc = await _firestore
              .collection('users')
              .doc(userId)
              .get();
          userData = userDoc.data();
        }

        result.add({
          'id': predDoc.id,
          'user_id': userId,
          'prediction': predData['prediction'] ?? '',
          'confidence': predData['confidence'] ?? 0.0,
          'risk_level': predData['riskLevel'] ?? 'Unknown',
          'symptoms': predData['symptoms'],
          'created_at':
              (predData['createdAt'] as Timestamp?)
                  ?.toDate()
                  .toIso8601String() ??
              '',
          'username': userData?['username'] ?? 'Unknown',
          'full_name': userData?['fullName'] ?? 'Unknown User',
        });
      }

      return result;
    } catch (e) {
      _logger.e('Error getting predictions by risk level: $e');
      return [];
    }
  }

  // ADMIN METHODS - Get user activity summary
  Future<Map<String, dynamic>> getUserActivitySummary() async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));

      // Get users who were active today
      final todayPredictions = await _firestore
          .collection('predictions')
          .where('createdAt', isGreaterThan: Timestamp.fromDate(today))
          .get();

      // Get users who were active yesterday
      final yesterdayPredictions = await _firestore
          .collection('predictions')
          .where('createdAt', isGreaterThan: Timestamp.fromDate(yesterday))
          .where('createdAt', isLessThan: Timestamp.fromDate(today))
          .get();

      final activeUsersToday = todayPredictions.docs
          .map((doc) => doc.data()['userId'])
          .toSet()
          .length;

      final activeUsersYesterday = yesterdayPredictions.docs
          .map((doc) => doc.data()['userId'])
          .toSet()
          .length;

      return {
        'active_users_today': activeUsersToday,
        'active_users_yesterday': activeUsersYesterday,
        'predictions_today': todayPredictions.docs.length,
        'predictions_yesterday': yesterdayPredictions.docs.length,
        'growth_rate': activeUsersYesterday > 0
            ? ((activeUsersToday - activeUsersYesterday) /
                      activeUsersYesterday *
                      100)
                  .toStringAsFixed(1)
            : '0.0',
      };
    } catch (e) {
      _logger.e('Error getting user activity summary: $e');
      return {
        'active_users_today': 0,
        'active_users_yesterday': 0,
        'predictions_today': 0,
        'predictions_yesterday': 0,
        'growth_rate': '0.0',
      };
    }
  }

  // Update user profile
  Future<void> updateUserProfile(app_user.User user) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      await _firestore.collection('users').doc(userId).update({
        'fullName': user.fullName,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update Firebase Auth display name
      await _auth.currentUser?.updateDisplayName(user.fullName);
    } catch (e) {
      _logger.e('Error updating user profile: $e');
      rethrow;
    }
  }

  // Get user profile
  Future<app_user.User?> getUserProfile() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        return null;
      }

      final doc = await _firestore.collection('users').doc(userId).get();
      if (!doc.exists) {
        return null;
      }

      final data = doc.data()!;
      return app_user.User(
        id: userId,
        username: data['username'] ?? '',
        email: data['email'] ?? '',
        fullName: data['fullName'] ?? '',
        createdAt:
            (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        isAdmin: data['isAdmin'] ?? false,
      );
    } catch (e) {
      _logger.e('Error getting user profile: $e');
      return null;
    }
  }
}
