import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';

class FirebaseDatabaseService {
  DatabaseReference? _dbRef;
  bool _isInitialized = false;

  FirebaseDatabaseService() {
    try {
      _dbRef = FirebaseDatabase.instance.ref('predictions');
      _isInitialized = true;
    } catch (e) {
      if (kDebugMode) {
        print('Firebase not configured: $e');
      }
      _isInitialized = false;
    }
  }

  Future<void> savePrediction(Map<String, dynamic> data) async {
    if (!_isInitialized || _dbRef == null) {
      if (kDebugMode) {
        print('Firebase not available - prediction not saved to cloud');
      }
      return; // Gracefully handle missing Firebase
    }

    try {
      await _dbRef!.push().set(data);
      if (kDebugMode) {
        print('Prediction saved to Firebase successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to save prediction to Firebase: $e');
      }
      // Don't throw error - app should continue working
    }
  }

  bool get isAvailable => _isInitialized;
}
