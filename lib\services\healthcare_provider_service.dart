import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/healthcare_provider.dart';

class HealthcareProviderService {
  static final HealthcareProviderService _instance =
      HealthcareProviderService._internal();
  factory HealthcareProviderService() => _instance;
  HealthcareProviderService._internal();

  // Sample healthcare providers (in production, this would come from a real database)
  static final List<HealthcareProvider> _sampleProviders = [
    HealthcareProvider(
      id: 1,
      name: 'City General Hospital',
      type: 'hospital',
      address: '123 Main Street, Downtown',
      phone: '******-0101',
      email: '<EMAIL>',
      services: [
        'Emergency Care',
        'HIV Testing',
        'HIV Treatment',
        'General Medicine',
      ],
      latitude: 40.7128,
      longitude: -74.0060,
      rating: 4.5,
      openingHours: '24/7',
      isEmergency: true,
      hasHivTesting: true,
      hasHivTreatment: true,
    ),
    HealthcareProvider(
      id: 2,
      name: 'Community Health Clinic',
      type: 'clinic',
      address: '456 Oak Avenue, Midtown',
      phone: '******-0102',
      email: '<EMAIL>',
      services: ['HIV Testing', 'STD Testing', 'Counseling', 'Preventive Care'],
      latitude: 40.7589,
      longitude: -73.9851,
      rating: 4.2,
      openingHours: 'Mon-Fri 8AM-6PM, Sat 9AM-3PM',
      isEmergency: false,
      hasHivTesting: true,
      hasHivTreatment: false,
    ),
    HealthcareProvider(
      id: 3,
      name: 'Rapid Testing Center',
      type: 'testing_center',
      address: '789 Pine Street, Uptown',
      phone: '******-0103',
      email: '<EMAIL>',
      services: ['HIV Testing', 'STD Testing', 'Rapid Results'],
      latitude: 40.7831,
      longitude: -73.9712,
      rating: 4.8,
      openingHours: 'Mon-Sun 7AM-10PM',
      isEmergency: false,
      hasHivTesting: true,
      hasHivTreatment: false,
    ),
    HealthcareProvider(
      id: 4,
      name: 'Metro Medical Center',
      type: 'hospital',
      address: '321 Elm Street, Westside',
      phone: '******-0104',
      email: '<EMAIL>',
      services: [
        'Emergency Care',
        'HIV Treatment',
        'Infectious Disease',
        'Pharmacy',
      ],
      latitude: 40.7505,
      longitude: -74.0134,
      rating: 4.3,
      openingHours: '24/7',
      isEmergency: true,
      hasHivTesting: true,
      hasHivTreatment: true,
    ),
    HealthcareProvider(
      id: 5,
      name: 'Wellness Pharmacy',
      type: 'pharmacy',
      address: '654 Maple Drive, Eastside',
      phone: '******-0105',
      email: '<EMAIL>',
      services: ['Prescription Medications', 'HIV Medications', 'Consultation'],
      latitude: 40.7282,
      longitude: -73.9942,
      rating: 4.0,
      openingHours: 'Mon-Sat 8AM-9PM, Sun 10AM-6PM',
      isEmergency: false,
      hasHivTesting: false,
      hasHivTreatment: true,
    ),
  ];

  Future<Position?> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (kDebugMode) {
          print('Location services are disabled.');
        }
        return null;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (kDebugMode) {
            print('Location permissions are denied');
          }
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (kDebugMode) {
          print('Location permissions are permanently denied');
        }
        return null;
      }

      // Get current position
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting location: $e');
      }
      return null;
    }
  }

  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2) /
        1000; // Convert to km
  }

  Future<List<HealthcareProvider>> findNearbyProviders({
    Position? userLocation,
    double radiusKm = 10.0,
    String? type,
    bool hivTestingOnly = false,
    bool hivTreatmentOnly = false,
    bool emergencyOnly = false,
  }) async {
    try {
      // Use current location if not provided
      userLocation ??= await getCurrentLocation();

      // If no location available, return all providers without distance
      if (userLocation == null) {
        List<HealthcareProvider> providers = List.from(_sampleProviders);

        // Apply filters
        if (type != null) {
          providers = providers.where((p) => p.type == type).toList();
        }
        if (hivTestingOnly) {
          providers = providers.where((p) => p.hasHivTesting).toList();
        }
        if (hivTreatmentOnly) {
          providers = providers.where((p) => p.hasHivTreatment).toList();
        }
        if (emergencyOnly) {
          providers = providers.where((p) => p.isEmergency).toList();
        }

        return providers;
      }

      // Calculate distances and filter by radius
      List<HealthcareProvider> nearbyProviders = [];

      for (HealthcareProvider provider in _sampleProviders) {
        double distance = _calculateDistance(
          userLocation.latitude,
          userLocation.longitude,
          provider.latitude,
          provider.longitude,
        );

        if (distance <= radiusKm) {
          // Create a new provider with calculated distance
          HealthcareProvider providerWithDistance = HealthcareProvider(
            id: provider.id,
            name: provider.name,
            type: provider.type,
            address: provider.address,
            phone: provider.phone,
            email: provider.email,
            services: provider.services,
            latitude: provider.latitude,
            longitude: provider.longitude,
            rating: provider.rating,
            openingHours: provider.openingHours,
            isEmergency: provider.isEmergency,
            hasHivTesting: provider.hasHivTesting,
            hasHivTreatment: provider.hasHivTreatment,
            isVerified: provider.isVerified,
            distanceKm: distance,
          );

          nearbyProviders.add(providerWithDistance);
        }
      }

      // Apply filters
      if (type != null) {
        nearbyProviders = nearbyProviders.where((p) => p.type == type).toList();
      }
      if (hivTestingOnly) {
        nearbyProviders = nearbyProviders
            .where((p) => p.hasHivTesting)
            .toList();
      }
      if (hivTreatmentOnly) {
        nearbyProviders = nearbyProviders
            .where((p) => p.hasHivTreatment)
            .toList();
      }
      if (emergencyOnly) {
        nearbyProviders = nearbyProviders.where((p) => p.isEmergency).toList();
      }

      // Sort by distance
      nearbyProviders.sort((a, b) => a.distanceKm.compareTo(b.distanceKm));

      return nearbyProviders;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding nearby providers: $e');
      }
      return [];
    }
  }

  Future<List<HealthcareProvider>> getProvidersForRiskLevel(
    String riskLevel,
  ) async {
    switch (riskLevel.toLowerCase()) {
      case 'high':
        // For high risk: Emergency hospitals and testing centers
        return await findNearbyProviders(radiusKm: 20.0, hivTestingOnly: true);
      case 'medium':
        // For medium risk: Testing centers and clinics
        return await findNearbyProviders(radiusKm: 15.0, hivTestingOnly: true);
      case 'low':
        // For low risk: General healthcare providers
        return await findNearbyProviders(radiusKm: 10.0);
      default:
        return await findNearbyProviders();
    }
  }

  Future<HealthcareProvider?> getNearestEmergencyProvider() async {
    final providers = await findNearbyProviders(
      radiusKm: 50.0,
      emergencyOnly: true,
    );

    return providers.isNotEmpty ? providers.first : null;
  }

  Future<HealthcareProvider?> getNearestTestingCenter() async {
    final providers = await findNearbyProviders(
      radiusKm: 25.0,
      hivTestingOnly: true,
    );

    return providers.isNotEmpty ? providers.first : null;
  }

  String getDirectionsUrl(
    HealthcareProvider provider, {
    Position? fromLocation,
  }) {
    if (fromLocation != null) {
      return 'https://www.google.com/maps/dir/${fromLocation.latitude},${fromLocation.longitude}/${provider.latitude},${provider.longitude}';
    } else {
      return 'https://www.google.com/maps/search/?api=1&query=${provider.latitude},${provider.longitude}';
    }
  }

  String getCallUrl(HealthcareProvider provider) {
    return 'tel:${provider.phone}';
  }
}
