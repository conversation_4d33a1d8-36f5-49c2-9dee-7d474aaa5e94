import 'dart:async';
import 'package:flutter/foundation.dart';
import 'prediction_service.dart';
import 'mock_prediction_service.dart';

class HybridPredictionService {
  static bool _useOfflineMode = false;
  static DateTime? _lastConnectionAttempt;
  static const Duration _connectionRetryInterval = Duration(minutes: 5);

  /// Main prediction method that tries online first, then falls back to offline
  static Future<Map<String, dynamic>> predictHiv(
    Map<String, int> symptoms,
  ) async {
    // Check if we should try online mode
    if (!_useOfflineMode || _shouldRetryConnection()) {
      try {
        // Try online prediction first
        final result = await HivPredictionService.predictHiv(
          symptoms,
        ).timeout(const Duration(seconds: 8));

        // Success! Reset offline mode
        _useOfflineMode = false;
        _lastConnectionAttempt = null;

        if (kDebugMode) {
          print('✅ Online prediction successful with your API');
        }

        // Add mode indicator to show it came from your API
        result['mode'] = 'online';
        result['api_source'] = 'your_api';
        return result;
      } catch (e) {
        if (kDebugMode) {
          print('❌ Online prediction failed: $e');
          print('🔄 Falling back to offline mode');
        }

        // Switch to offline mode
        _useOfflineMode = true;
        _lastConnectionAttempt = DateTime.now();
      }
    }

    // Use offline prediction
    if (kDebugMode) {
      print('📱 Using offline prediction');
    }

    final result = await MockPredictionService.predictHiv(symptoms);
    result['mode'] = 'offline';
    return result;
  }

  /// Prediction with location tracking
  static Future<Map<String, dynamic>> predictHivWithLocations(
    Map<String, dynamic> data,
  ) async {
    // Check if we should try online mode
    if (!_useOfflineMode || _shouldRetryConnection()) {
      try {
        // Try online prediction first
        final result = await HivPredictionService.predictHivWithLocations(
          data,
        ).timeout(const Duration(seconds: 8));

        // Success! Reset offline mode
        _useOfflineMode = false;
        _lastConnectionAttempt = null;

        if (kDebugMode) {
          print('✅ Online prediction with locations successful with your API');
        }

        // Add mode indicator to show it came from your API
        result['mode'] = 'online';
        result['api_source'] = 'your_api';
        return result;
      } catch (e) {
        if (kDebugMode) {
          print('❌ Online prediction with locations failed: $e');
          print('🔄 Falling back to offline mode');
        }

        // Switch to offline mode
        _useOfflineMode = true;
        _lastConnectionAttempt = DateTime.now();
      }
    }

    // Use offline prediction
    if (kDebugMode) {
      print('📱 Using offline prediction with locations');
    }

    final result = await MockPredictionService.predictHivWithLocations(data);
    result['mode'] = 'offline';
    return result;
  }

  /// Check if we should retry online connection
  static bool _shouldRetryConnection() {
    if (_lastConnectionAttempt == null) return true;

    final timeSinceLastAttempt = DateTime.now().difference(
      _lastConnectionAttempt!,
    );
    return timeSinceLastAttempt >= _connectionRetryInterval;
  }

  /// Get current service status
  static Future<Map<String, dynamic>> getServiceStatus() async {
    if (!_useOfflineMode || _shouldRetryConnection()) {
      // Try to check online service
      try {
        final isOnline = await HivPredictionService.checkHealth().timeout(
          const Duration(seconds: 3),
        );

        if (isOnline) {
          _useOfflineMode = false;
          _lastConnectionAttempt = null;

          return {
            'mode': 'online',
            'status': 'connected',
            'message': 'Your AI API is available',
            'accuracy': 'High (Your AI Model)',
            'icon': '🤖',
          };
        }
      } catch (e) {
        _useOfflineMode = true;
        _lastConnectionAttempt = DateTime.now();
      }
    }

    // Return offline status
    return {
      'mode': 'offline',
      'status': 'offline',
      'message': 'Using offline risk assessment',
      'accuracy': 'Good (Rule-based)',
      'icon': '📱',
      'next_retry': _lastConnectionAttempt
          ?.add(_connectionRetryInterval)
          .toIso8601String(),
    };
  }

  /// Force retry online connection
  static void forceRetryOnline() {
    _useOfflineMode = false;
    _lastConnectionAttempt = null;

    if (kDebugMode) {
      print('🔄 Forcing retry of online connection');
    }
  }

  /// Get user-friendly status message
  static Future<String> getStatusMessage() async {
    final status = await getServiceStatus();

    switch (status['mode']) {
      case 'online':
        return '🤖 Your AI API is connected';
      case 'offline':
        return '📱 Offline mode - Rule-based assessment';
      default:
        return '⚠️ Checking connection...';
    }
  }

  /// Check if currently in offline mode
  static bool get isOfflineMode => _useOfflineMode;

  /// Get time until next connection retry
  static Duration? get timeUntilRetry {
    if (_lastConnectionAttempt == null) return null;

    final nextRetry = _lastConnectionAttempt!.add(_connectionRetryInterval);
    final now = DateTime.now();

    if (nextRetry.isAfter(now)) {
      return nextRetry.difference(now);
    }

    return null;
  }
}
