import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageService {
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  static const String _languageKey = 'selected_language';
  String _currentLanguage = 'en';

  // Get translations for current language
  Map<String, String> get texts =>
      _translations[_currentLanguage] ?? _translations['en']!;

  // Language translations
  static const Map<String, Map<String, String>> _translations = {
    'en': {
      // Landing Screen
      'app_title': 'HIV Predictor',
      'app_subtitle':
          'AI-powered HIV risk assessment and healthcare navigation',
      'feature_ai': 'AI-Powered Assessment',
      'feature_ai_desc': 'Advanced algorithms for accurate risk evaluation',
      'feature_location': 'Hospital Finder',
      'feature_location_desc': 'Find nearby healthcare facilities instantly',
      'feature_education': 'HIV Education',
      'feature_education_desc': 'Comprehensive information and resources',
      'get_started': 'Get Started',
      'sign_in': 'Sign In',
      'footer_text': 'Your health, our priority',

      // Navigation
      'predict': 'Predict',
      'education': 'Education',
      'map': 'Map',
      'settings': 'Settings',
      'dashboard': 'Dashboard',
      'home': 'Home',
      'logout': 'Logout',
      'profile': 'Profile',

      // Education Screen
      'hiv_education': 'HIV Education',
      'search_topics': 'Search topics...',
      'bookmark': 'Bookmark',
      'bookmarked': 'Bookmarked',
      'watch_video': 'Watch Educational Video',
      'read_more': 'Read More',
      'prevention': 'Prevention',
      'symptoms': 'Symptoms',
      'testing': 'Testing',
      'treatment': 'Treatment',
      'support': 'Support',
      'myths_facts': 'Myths & Facts',

      // Settings
      'notifications': 'Notifications',
      'privacy_data': 'Privacy & Data',
      'appearance': 'Appearance',
      'accessibility': 'Accessibility',
      'security': 'Security',
      'emergency': 'Emergency',
      'advanced': 'Advanced',

      // Common
      'yes': 'Yes',
      'no': 'No',
      'sometimes': 'Sometimes',
      'save': 'Save',
      'cancel': 'Cancel',
      'ok': 'OK',
      'error': 'Error',
      'success': 'Success',
      'loading': 'Loading...',

      // Symptoms - Enhanced with all 22 symptoms
      'fever': 'Fever',
      'headache': 'Headache',
      'skin_rash': 'Skin Rash',
      'muscle_pain': 'Muscle Pain',
      'weight_loss': 'Weight Loss',
      'fatigue': 'Fatigue',
      'oral_ulcers': 'Oral Ulcers',
      'swollen_lymph_nodes': 'Swollen Lymph Nodes',
      'diarrhea': 'Diarrhea',
      'night_sweats': 'Night Sweats',

      // New early/acute symptoms
      'sore_throat': 'Sore Throat',
      'joint_pain': 'Joint Pain',
      'nausea': 'Nausea',
      'loss_of_appetite': 'Loss of Appetite',
      'chills': 'Chills',
      'persistent_cough': 'Persistent Cough',

      // New advanced symptoms
      'recurring_infections': 'Recurring Infections',
      'memory_problems': 'Memory Problems',
      'vision_problems': 'Vision Problems',
      'persistent_headaches': 'Persistent Headaches',

      // Risk behaviors
      'unprotected_sex': 'Unprotected Sex',
      'shared_needles': 'Shared Needles',
      'multiple_partners': 'Multiple Partners',
      'blood_transfusion': 'Recent Blood Transfusion',
      'tattoo_piercing_unsterile': 'Unsterile Tattoo/Piercing',
      'partner_hiv_positive': 'Partner with HIV/Unknown Status',

      // Symptom sections
      'common_physical_symptoms': 'Common Physical Symptoms',
      'early_infection_symptoms': 'Early Infection Symptoms',
      'advanced_symptoms': 'Advanced Symptoms',
      'additional_risk_factors': 'Additional Risk Factors',
      'risk_behaviors': 'Risk Behaviors',

      // Hospital finder
      'hospital_map': 'Hospital Map',
      'find_hospitals': 'Find Hospitals',
      'nearest_hospital': 'Nearest Hospital',
      'get_directions': 'Get Directions',
      'call_hospital': 'Call Hospital',
      'location_error': 'Location Error',
      'location_permission_denied': 'Location permission denied',
      'enable_location': 'Enable Location',
      'retry': 'Retry',
      'refresh_location': 'Refresh Location',
      'later': 'Later',
      'current_location': 'Current Location',
      'no_hospitals': 'No hospitals found nearby',
      'call': 'Call',
      'directions': 'Directions',

      // Map and Hospital Features
      'high_risk_alert': 'High Risk Assessment',
      'high_risk_message':
          'Your assessment indicates a high risk for HIV. It is strongly recommended that you:',

      // Home Screen
      'welcome_message': 'Take care of your health today',
      'quick_actions': 'Quick Actions',
      'new_assessment': 'New Assessment',
      'check_risk': 'Check your risk',
      'nearby_care': 'Nearby care',
      'learn_more': 'Learn more',
      'view_results': 'View results',
      'your_stats': 'Your Statistics',
      'total_assessments': 'Total Assessments',
      'last_check': 'Last Check',
      'never': 'Never',
      'recent_activity': 'Recent Activity',
      'view_all': 'View All',
      'no_assessments': 'No assessments yet',
      'health_tips': 'Health Tips',
      'tip_prevention': 'Use protection during sexual activity',
      'tip_testing': 'Get tested regularly if you\'re at risk',
      'tip_health': 'Maintain a healthy lifestyle',

      // Authentication
      'sign_in_to_continue': 'Sign in to access all features',
      'auth_description':
          'Track your assessments, view history, and get personalized recommendations',
      'sign_up': 'Sign Up',
      'available_features': 'Available Features',
      'learn_about_hiv': 'Learn about HIV',
      'locate_healthcare': 'Locate healthcare',
    },

    'fr': {
      // Landing Screen
      'app_title': 'Prédicteur VIH',
      'app_subtitle':
          'Évaluation des risques VIH et navigation santé alimentées par IA',
      'feature_ai': 'Évaluation par IA',
      'feature_ai_desc':
          'Algorithmes avancés pour une évaluation précise des risques',
      'feature_location': 'Localisateur d\'Hôpitaux',
      'feature_location_desc':
          'Trouvez instantanément les établissements de santé à proximité',
      'feature_education': 'Éducation VIH',
      'feature_education_desc': 'Informations et ressources complètes',
      'get_started': 'Commencer',
      'sign_in': 'Se Connecter',
      'footer_text': 'Votre santé, notre priorité',

      // Navigation
      'predict': 'Prédire',
      'education': 'Éducation',
      'map': 'Carte',
      'settings': 'Paramètres',
      'dashboard': 'Tableau de Bord',
      'home': 'Accueil',
      'logout': 'Déconnexion',
      'profile': 'Profil',

      // Education Screen
      'hiv_education': 'Éducation VIH',
      'search_topics': 'Rechercher des sujets...',
      'bookmark': 'Marquer',
      'bookmarked': 'Marqué',
      'watch_video': 'Regarder la Vidéo Éducative',
      'read_more': 'Lire Plus',
      'prevention': 'Prévention',
      'symptoms': 'Symptômes',
      'testing': 'Dépistage',
      'treatment': 'Traitement',
      'support': 'Soutien',
      'myths_facts': 'Mythes et Réalités',

      // Settings
      'notifications': 'Notifications',
      'privacy_data': 'Confidentialité et Données',
      'appearance': 'Apparence',
      'accessibility': 'Accessibilité',
      'security': 'Sécurité',
      'emergency': 'Urgence',
      'advanced': 'Avancé',

      // Common
      'yes': 'Oui',
      'no': 'Non',
      'sometimes': 'Parfois',
      'save': 'Enregistrer',
      'cancel': 'Annuler',
      'ok': 'OK',
      'error': 'Erreur',
      'success': 'Succès',
      'loading': 'Chargement...',

      // Symptoms - Enhanced with all 22 symptoms
      'fever': 'Fièvre',
      'headache': 'Mal de Tête',
      'skin_rash': 'Éruption Cutanée',
      'muscle_pain': 'Douleur Musculaire',
      'weight_loss': 'Perte de Poids Inexpliquée',
      'fatigue': 'Fatigue Persistante',
      'oral_ulcers': 'Ulcères Buccaux',
      'swollen_lymph_nodes': 'Ganglions Lymphatiques Enflés',
      'diarrhea': 'Diarrhée Chronique',
      'night_sweats': 'Sueurs Nocturnes',

      // New early/acute symptoms
      'sore_throat': 'Mal de Gorge',
      'joint_pain': 'Douleur Articulaire',
      'nausea': 'Nausée',
      'loss_of_appetite': 'Perte d\'Appétit',
      'chills': 'Frissons',
      'persistent_cough': 'Toux Persistante',

      // New advanced symptoms
      'recurring_infections': 'Infections Récurrentes',
      'memory_problems': 'Problèmes de Mémoire',
      'vision_problems': 'Problèmes de Vision',
      'persistent_headaches': 'Maux de Tête Persistants',

      // Risk behaviors
      'unprotected_sex': 'Rapports Non Protégés',
      'shared_needles': 'Aiguilles Partagées',
      'multiple_partners': 'Partenaires Multiples',
      'blood_transfusion': 'Transfusion Sanguine Récente',
      'tattoo_piercing_unsterile': 'Tatouage/Piercing Non Stérilisé',
      'partner_hiv_positive': 'Partenaire VIH+/Statut Inconnu',

      // Symptom sections
      'common_physical_symptoms': 'Symptômes Physiques Courants',
      'early_infection_symptoms': 'Symptômes d\'Infection Précoce',
      'advanced_symptoms': 'Symptômes Avancés',
      'additional_risk_factors': 'Facteurs de Risque Supplémentaires',
      'risk_behaviors': 'Comportements à Risque',

      // Hospital finder
      'hospital_map': 'Carte des Hôpitaux',
      'find_hospitals': 'Trouver des Hôpitaux',
      'nearest_hospital': 'Hôpital le Plus Proche',
      'get_directions': 'Obtenir l\'Itinéraire',
      'call_hospital': 'Appeler l\'Hôpital',
      'location_error': 'Erreur de Localisation',
      'location_permission_denied': 'Permission de localisation refusée',
      'enable_location': 'Activer la Localisation',
      'retry': 'Réessayer',
      'refresh_location': 'Actualiser la Position',
      'later': 'Plus Tard',
      'current_location': 'Position Actuelle',
      'no_hospitals': 'Aucun hôpital trouvé à proximité',
      'call': 'Appeler',
      'directions': 'Itinéraire',

      // Map and Hospital Features
      'high_risk_alert': 'Évaluation à Haut Risque',
      'high_risk_message':
          'Votre évaluation indique un risque élevé de VIH. Il est fortement recommandé de:',

      // Home Screen
      'welcome_message': 'Prenez soin de votre santé aujourd\'hui',
      'quick_actions': 'Actions Rapides',
      'new_assessment': 'Nouvelle Évaluation',
      'check_risk': 'Vérifiez votre risque',
      'nearby_care': 'Soins à proximité',
      'learn_more': 'En savoir plus',
      'view_results': 'Voir les résultats',
      'your_stats': 'Vos Statistiques',
      'total_assessments': 'Évaluations Totales',
      'last_check': 'Dernière Vérification',
      'never': 'Jamais',
      'recent_activity': 'Activité Récente',
      'view_all': 'Voir Tout',
      'no_assessments': 'Aucune évaluation encore',
      'health_tips': 'Conseils Santé',
      'tip_prevention': 'Utilisez une protection lors des rapports sexuels',
      'tip_testing': 'Faites-vous tester régulièrement si vous êtes à risque',
      'tip_health': 'Maintenez un mode de vie sain',

      // Authentication
      'sign_in_to_continue':
          'Connectez-vous pour accéder à toutes les fonctionnalités',
      'auth_description':
          'Suivez vos évaluations, consultez l\'historique et obtenez des recommandations personnalisées',
      'sign_up': 'S\'inscrire',
      'available_features': 'Fonctionnalités Disponibles',
      'learn_about_hiv': 'Apprendre sur le VIH',
      'locate_healthcare': 'Localiser les soins',
    },

    'rw': {
      // Landing Screen
      'app_title': 'Ubushakashatsi bwa SIDA',
      'app_subtitle':
          'Gusuzuma ibyago bya SIDA hamwe n\'ubuvuzi bw\'ubwiyunge bwakozwe na AI',
      'feature_ai': 'Isuzuma rya AI',
      'feature_ai_desc': 'Tekinoroji igezweho yo gusuzuma ibyago neza',
      'feature_location': 'Gushaka Ibitaro',
      'feature_location_desc':
          'Shakisha ibigo by\'ubuvuzi biri hafi yawe ako kanya',
      'feature_education': 'Uburezi ku SIDA',
      'feature_education_desc': 'Amakuru n\'ibikoresho byuzuye',
      'get_started': 'Tangira',
      'sign_in': 'Injira',
      'footer_text': 'Ubuzima bwawe, intego yacu',

      // Navigation
      'predict': 'Guhanga',
      'education': 'Uburezi',
      'map': 'Ikarita',
      'settings': 'Igenamiterere',
      'dashboard': 'Ikibaho',
      'home': 'Ahabanza',
      'logout': 'Gusohoka',
      'profile': 'Umwirondoro',

      // Education Screen
      'hiv_education': 'Uburezi ku SIDA',
      'search_topics': 'Shakisha ingingo...',
      'bookmark': 'Gushyira akamenyetso',
      'bookmarked': 'Hashyizweho akamenyetso',
      'watch_video': 'Kureba Amashusho y\'Uburezi',
      'read_more': 'Soma Byinshi',
      'prevention': 'Kwirinda',
      'symptoms': 'Ibimenyetso',
      'testing': 'Kwipimisha',
      'treatment': 'Kuvura',
      'support': 'Gufasha',
      'myths_facts': 'Ibinyoma n\'Ukuri',

      // Settings
      'notifications': 'Ubutumwa',
      'privacy_data': 'Ibanga n\'Amakuru',
      'appearance': 'Isura',
      'accessibility': 'Kugera',
      'security': 'Umutekano',
      'emergency': 'Byihutirwa',
      'advanced': 'Byimbitse',

      // Common
      'yes': 'Yego',
      'no': 'Oya',
      'sometimes': 'Rimwe na rimwe',
      'save': 'Bika',
      'cancel': 'Kuraguza',
      'ok': 'Sawa',
      'error': 'Ikosa',
      'success': 'Byagenze neza',
      'loading': 'Biratangura...',

      // Symptoms - Enhanced with all 22 symptoms
      'fever': 'Umuriro',
      'headache': 'Kubabara umutwe',
      'skin_rash': 'Ibisebe ku ruhu',
      'muscle_pain': 'Kubabara imitsi',
      'weight_loss': 'Kugabanuka ibiro bitunguranye',
      'fatigue': 'Umunaniro ukomeje',
      'oral_ulcers': 'Ibikomere mu kanwa',
      'swollen_lymph_nodes': 'Imisemburo ibyimbye',
      'diarrhea': 'Impiswi ikomeje',
      'night_sweats': 'Gucuraguza nijoro',

      // New early/acute symptoms
      'sore_throat': 'Kubabara umuhogo',
      'joint_pain': 'Kubabara ingingo',
      'nausea': 'Kuraguza',
      'loss_of_appetite': 'Kutifuza kurya',
      'chills': 'Guhinda',
      'persistent_cough': 'Inkorora ikomeje',

      // New advanced symptoms
      'recurring_infections': 'Indwara zigaruka',
      'memory_problems': 'Ibibazo byo kwibuka',
      'vision_problems': 'Ibibazo byo kureba',
      'persistent_headaches': 'Kubabara umutwe bikomeje',

      // Risk behaviors
      'unprotected_sex': 'Imibonano idafite uburinzi',
      'shared_needles': 'Urushinge rwasangiwe',
      'multiple_partners': 'Ababyeyi benshi',
      'blood_transfusion': 'Guterwa amaraso vuba',
      'tattoo_piercing_unsterile': 'Gushushanya/Gutunga bidafite isuku',
      'partner_hiv_positive': 'Umubyeyi ufite SIDA/Bitazwi',

      // Symptom sections
      'common_physical_symptoms': 'Ibimenyetso Bisanzwe by\'Umubiri',
      'early_infection_symptoms': 'Ibimenyetso by\'Indwara yo Hambere',
      'advanced_symptoms': 'Ibimenyetso Byimbitse',
      'additional_risk_factors': 'Ibindi Bintu Bitera Ibyago',
      'risk_behaviors': 'Imyitwarire Itera Ibyago',

      // Hospital finder
      'hospital_map': 'Ikarita y\'Ibitaro',
      'find_hospitals': 'Shakisha Ibitaro',
      'nearest_hospital': 'Ibitaro Biri Hafi',
      'get_directions': 'Kubona Inzira',
      'call_hospital': 'Guhamagara Ibitaro',
      'location_error': 'Ikosa mu Gushaka Aho Uri',
      'location_permission_denied': 'Uruhushya rwo kumenya aho uri rwanze',
      'enable_location': 'Kwemerera Kumenya Aho Uri',
      'retry': 'Ongera Ugerageze',
      'refresh_location': 'Kuvugurura Aho Uri',
      'later': 'Nyuma',
      'current_location': 'Aho Uri',
      'no_hospitals': 'Nta bitaro byabonetse hafi',
      'call': 'Hamagara',
      'directions': 'Inzira',

      // Map and Hospital Features
      'high_risk_alert': 'Isuzuma ry\'Ibyago Byinshi',
      'high_risk_message':
          'Isuzuma ryawe ryerekana ibyago byinshi bya SIDA. Birasabwa cyane:',

      // Home Screen
      'welcome_message': 'Witondere ubuzima bwawe uyu munsi',
      'quick_actions': 'Ibikorwa Byihuse',
      'new_assessment': 'Isuzuma Rishya',
      'check_risk': 'Reba ibyago byawe',
      'nearby_care': 'Ubuvuzi buri hafi',
      'learn_more': 'Wige byinshi',
      'view_results': 'Reba ibisubizo',
      'your_stats': 'Imibare Yawe',
      'total_assessments': 'Amasuzuma Yose',
      'last_check': 'Isuzuma Rya Nyuma',
      'never': 'Nta narimwe',
      'recent_activity': 'Ibikorwa Bya Vuba',
      'view_all': 'Reba Byose',
      'no_assessments': 'Nta masuzuma akaba',
      'health_tips': 'Inama z\'Ubuzima',
      'tip_prevention': 'Koresha uburinzi mu mibonano mpuzabitsina',
      'tip_testing': 'Wipimishe buri gihe niba ufite ibyago',
      'tip_health': 'Komeza ubuzima bwiza',

      // Authentication
      'sign_in_to_continue': 'Injira kugira ngo ubone ibintu byose',
      'auth_description':
          'Kugenzura amasuzuma yawe, kureba amateka no kubona inama zihariye',
      'sign_up': 'Kwiyandikisha',
      'available_features': 'Ibintu Bihari',
      'learn_about_hiv': 'Wige ku SIDA',
      'locate_healthcare': 'Shakisha ubuvuzi',
    },
  };

  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentLanguage = prefs.getString(_languageKey) ?? 'en';
    } catch (e) {
      _currentLanguage = 'en';
    }
  }

  Future<String> getCurrentLanguage() async {
    await initialize();
    return _currentLanguage;
  }

  Future<bool> setLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final success = await prefs.setString(_languageKey, languageCode);
      if (success) {
        _currentLanguage = languageCode;
        // Notify all listeners about language change
        _notifyLanguageChange();
      }
      return success;
    } catch (e) {
      return false;
    }
  }

  // Add callback system for language changes
  final List<VoidCallback> _languageChangeListeners = [];

  void addLanguageChangeListener(VoidCallback listener) {
    _languageChangeListeners.add(listener);
  }

  void removeLanguageChangeListener(VoidCallback listener) {
    _languageChangeListeners.remove(listener);
  }

  void _notifyLanguageChange() {
    for (final listener in _languageChangeListeners) {
      listener();
    }
  }

  Map<String, String> getTexts(String languageCode) {
    return _translations[languageCode] ?? _translations['en']!;
  }

  String getText(String key, [String? languageCode]) {
    final lang = languageCode ?? _currentLanguage;
    return _translations[lang]?[key] ?? _translations['en']?[key] ?? key;
  }

  List<String> getSupportedLanguages() {
    return _translations.keys.toList();
  }

  String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'fr':
        return 'Français';
      case 'rw':
        return 'Kinyarwanda';
      default:
        return 'English';
    }
  }
}
