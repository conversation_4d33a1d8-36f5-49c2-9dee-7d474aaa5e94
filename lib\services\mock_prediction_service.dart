import 'dart:math';

class MockPredictionService {
  /// Provides offline HIV risk assessment using rule-based logic
  /// This is a fallback when the AI server is not available

  static Future<Map<String, dynamic>> predictHiv(
    Map<String, int> symptoms,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1500));

    return _calculateRiskLevel(symptoms);
  }

  static Future<Map<String, dynamic>> predictHivWithLocations(
    Map<String, dynamic> data,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1500));

    final symptoms = Map<String, int>.from(data['symptoms'] ?? {});
    final result = _calculateRiskLevel(symptoms);

    // Add location information if provided
    final exposureLocations = data['exposure_locations'] ?? {};
    result['exposure_locations'] = exposureLocations;

    // Add location warning for high-risk behaviors with locations
    if (result['prediction'].toString().contains('High') &&
        exposureLocations.values.any(
          (location) => location.toString().isNotEmpty,
        )) {
      result['location_warning'] =
          'Consider informing healthcare providers about exposure locations for contact tracing if needed.';
    }

    return result;
  }

  static Map<String, dynamic> _calculateRiskLevel(Map<String, int> symptoms) {
    int riskScore = 0;
    List<String> riskFactors = [];

    // High-risk behaviors (weighted heavily)
    if (symptoms['unprotected_sex'] == 2) {
      // Yes
      riskScore += 40;
      riskFactors.add('Unprotected sexual activity');
    } else if (symptoms['unprotected_sex'] == 1) {
      // Sometimes
      riskScore += 25;
      riskFactors.add('Occasional unprotected sexual activity');
    }

    if (symptoms['shared_needles'] == 2) {
      // Yes
      riskScore += 50;
      riskFactors.add('Needle sharing');
    } else if (symptoms['shared_needles'] == 1) {
      // Sometimes
      riskScore += 35;
      riskFactors.add('Occasional needle sharing');
    }

    if (symptoms['multiple_partners'] == 2) {
      // Yes
      riskScore += 30;
      riskFactors.add('Multiple sexual partners');
    } else if (symptoms['multiple_partners'] == 1) {
      // Sometimes
      riskScore += 20;
      riskFactors.add('Occasional multiple partners');
    }

    // New high-risk factors
    if (symptoms['partner_hiv_positive'] == 2) {
      // Yes
      riskScore += 60;
      riskFactors.add('HIV-positive partner');
    } else if (symptoms['partner_hiv_positive'] == 1) {
      // Sometimes
      riskScore += 40;
      riskFactors.add('Possible HIV-positive partner');
    }

    if (symptoms['blood_transfusion'] == 2) {
      // Yes
      riskScore += 25;
      riskFactors.add('Recent blood transfusion');
    } else if (symptoms['blood_transfusion'] == 1) {
      // Sometimes
      riskScore += 15;
      riskFactors.add('Possible blood transfusion risk');
    }

    if (symptoms['tattoo_piercing_unsterile'] == 2) {
      // Yes
      riskScore += 20;
      riskFactors.add('Unsterile tattoo/piercing');
    } else if (symptoms['tattoo_piercing_unsterile'] == 1) {
      // Sometimes
      riskScore += 12;
      riskFactors.add('Possible unsterile tattoo/piercing');
    }

    // Early HIV symptoms (moderate weight)
    final earlySymptoms = [
      'fever',
      'headache',
      'muscle_pain',
      'fatigue',
      'skin_rash',
      'sore_throat',
      'joint_pain',
      'nausea',
      'chills',
    ];

    int symptomCount = 0;
    for (String symptom in earlySymptoms) {
      if (symptoms[symptom] == 2) {
        // Yes
        riskScore += 8;
        symptomCount++;
      } else if (symptoms[symptom] == 1) {
        // Sometimes
        riskScore += 5;
        symptomCount++;
      }
    }

    // Advanced symptoms (higher weight)
    final advancedSymptoms = [
      'weight_loss',
      'oral_ulcers',
      'swollen_lymph_nodes',
      'diarrhea',
      'night_sweats',
      'loss_of_appetite',
      'persistent_cough',
      'recurring_infections',
      'memory_problems',
      'vision_problems',
      'persistent_headaches',
    ];

    for (String symptom in advancedSymptoms) {
      if (symptoms[symptom] == 2) {
        // Yes
        riskScore += 12;
        symptomCount++;
      } else if (symptoms[symptom] == 1) {
        // Sometimes
        riskScore += 8;
        symptomCount++;
      }
    }

    // Bonus risk for multiple symptoms
    if (symptomCount >= 5) {
      riskScore += 15;
      riskFactors.add('Multiple symptoms present');
    } else if (symptomCount >= 3) {
      riskScore += 8;
    }

    // Determine risk level and confidence
    String riskLevel;
    double confidence;

    if (riskScore >= 70) {
      riskLevel = 'High Risk';
      confidence = 0.85 + (Random().nextDouble() * 0.1); // 85-95%
    } else if (riskScore >= 35) {
      riskLevel = 'Medium Risk';
      confidence = 0.75 + (Random().nextDouble() * 0.15); // 75-90%
    } else {
      riskLevel = 'Low Risk';
      confidence = 0.70 + (Random().nextDouble() * 0.20); // 70-90%
    }

    // Ensure confidence doesn't exceed 95% for mock predictions
    confidence = confidence.clamp(0.70, 0.95);

    return {
      'prediction': riskLevel,
      'confidence': confidence,
      'features_used': symptoms.keys
          .where((key) => symptoms[key]! > 0)
          .toList(),
      'risk_factors': riskFactors,
      'risk_score': riskScore,
      'is_mock': true, // Flag to indicate this is a mock prediction
      'disclaimer':
          'This is an offline assessment. For accurate results, please consult healthcare professionals.',
    };
  }

  static Future<bool> checkHealth() async {
    // Mock service is always "healthy"
    return true;
  }

  static Future<Map<String, dynamic>> getConnectionStatus() async {
    return {
      'connected': true,
      'url': 'offline-mode',
      'message': 'Using offline risk assessment',
      'prediction_ready': true,
      'last_check': DateTime.now().toIso8601String(),
      'mode': 'offline',
    };
  }
}
