// lib/services/hiv_prediction_service.dart

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class HivPredictionService {
  // API server addresses for different environments
  static const List<String> possibleUrls = [
    'http://127.0.0.1:5000', // Primary localhost (Your API server)
    'http://localhost:5000', // Alternative localhost (Your API server)
    'http://********:5000', // Android emulator (Your API server)
    'http://127.0.0.1:5001', // Fallback to Flask backend
    'http://localhost:5001', // Fallback to Flask backend
    'http://********:5001', // Android emulator (Flask backend)
    'http://**************:5000', // Physical devices (current network IP)
    'http://*************:5000', // Common local network IP
    'http://*************:5000', // Another common local network IP
  ];

  static String? _workingUrl;
  static DateTime? _lastUrlCheck;
  static const Duration _urlCacheTimeout = Duration(minutes: 5);

  /// Find a working API URL by testing each possible URL
  static Future<String?> _findWorkingUrl() async {
    // Check if we have a cached working URL that's still valid
    if (_workingUrl != null &&
        _lastUrlCheck != null &&
        DateTime.now().difference(_lastUrlCheck!) < _urlCacheTimeout) {
      return _workingUrl;
    }

    // Test each URL to find one that works
    for (String url in possibleUrls) {
      try {
        if (kDebugMode) {
          print('Testing API URL: $url');
        }

        // Try your API server first (port 5000), then Flask backend (port 5001)
        final healthEndpoint = url.contains('5000')
            ? '$url/health'
            : '$url/api/health';
        final response = await http
            .get(
              Uri.parse(healthEndpoint),
              headers: {'Accept': 'application/json'},
            )
            .timeout(const Duration(seconds: 3));

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          if (data['status'] == 'healthy') {
            _workingUrl = url;
            _lastUrlCheck = DateTime.now();
            if (kDebugMode) {
              print('Found working API URL: $url');
            }
            return url;
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Failed to connect to $url: $e');
        }
        continue;
      }
    }

    // Clear cached URL if none work
    _workingUrl = null;
    _lastUrlCheck = null;
    return null;
  }

  static Future<Map<String, dynamic>> predictHiv(
    Map<String, int> symptoms,
  ) async {
    final baseUrl = await _findWorkingUrl();
    if (baseUrl == null) {
      throw Exception(
        'Cannot connect to API server. Please ensure the server is running.',
      );
    }

    // Use correct endpoint based on server type
    final predictEndpoint = baseUrl.contains('5000')
        ? '$baseUrl/predict'
        : '$baseUrl/api/predict';
    final uri = Uri.parse(predictEndpoint);

    try {
      final response = await http
          .post(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(symptoms),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'prediction': data['prediction'],
          'confidence': data['confidence'],
          'features_used': data['features_used'],
        };
      } else {
        // Only clear cached URL on severe server errors (500+)
        if (response.statusCode >= 500) {
          if (kDebugMode) {
            print(
              'Server error ${response.statusCode}, will retry with different URL next time',
            );
          }
          refreshUrlCache();
        }

        final error = jsonDecode(response.body);
        throw Exception(
          error['error'] ?? 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      // Only clear cache on connection timeout or network errors, not on data errors
      if (e.toString().contains('timeout') ||
          e.toString().contains('connection')) {
        if (kDebugMode) {
          print(
            'Connection error, will retry with different URL next time: $e',
          );
        }
        refreshUrlCache();
      }
      throw Exception('Failed to connect to prediction service: $e');
    }
  }

  static Future<Map<String, dynamic>> predictHivWithLocations(
    Map<String, dynamic> data,
  ) async {
    final baseUrl = await _findWorkingUrl();
    if (baseUrl == null) {
      throw Exception(
        'Cannot connect to API server. Please ensure the server is running.',
      );
    }

    // Use correct endpoint based on server type
    final predictEndpoint = baseUrl.contains('5000')
        ? '$baseUrl/predict'
        : '$baseUrl/api/predict';
    final uri = Uri.parse(predictEndpoint);

    try {
      final response = await http
          .post(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(data),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return {
          'prediction': responseData['prediction'],
          'confidence': responseData['confidence'],
          'features_used': responseData['features_used'],
          'exposure_locations': responseData['exposure_locations'],
          'location_warning': responseData['location_warning'],
        };
      } else {
        // Only clear cached URL on severe server errors (500+)
        if (response.statusCode >= 500) {
          if (kDebugMode) {
            print(
              'Server error ${response.statusCode}, will retry with different URL next time',
            );
          }
          refreshUrlCache();
        }

        final error = jsonDecode(response.body);
        throw Exception(
          error['error'] ?? 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      // Only clear cache on connection timeout or network errors, not on data errors
      if (e.toString().contains('timeout') ||
          e.toString().contains('connection')) {
        if (kDebugMode) {
          print(
            'Connection error, will retry with different URL next time: $e',
          );
        }
        refreshUrlCache();
      }
      throw Exception('Failed to connect to prediction service: $e');
    }
  }

  static Future<bool> checkHealth() async {
    try {
      final baseUrl = await _findWorkingUrl();
      if (baseUrl == null) {
        return false;
      }

      final healthEndpoint = baseUrl.contains('5000')
          ? '$baseUrl/health'
          : '$baseUrl/api/health';
      final response = await http.get(Uri.parse(healthEndpoint));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['status'] == 'healthy' && data['model_loaded'] == true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Get the current working URL (for debugging purposes)
  static String? get currentWorkingUrl => _workingUrl;

  /// Force refresh the working URL cache
  static void refreshUrlCache() {
    _workingUrl = null;
    _lastUrlCheck = null;
  }

  /// Enhanced connection monitoring with retry logic
  static Future<bool> ensureConnection({int maxRetries = 3}) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final url = await _findWorkingUrl();
        if (url != null) {
          if (kDebugMode) {
            print('✅ API connection established on attempt $attempt');
          }
          return true;
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Connection attempt $attempt failed: $e');
        }
      }

      // Exponential backoff: wait longer between retries
      if (attempt < maxRetries) {
        final delay = Duration(seconds: attempt * 2);
        if (kDebugMode) {
          print('⏳ Waiting ${delay.inSeconds}s before retry...');
        }
        await Future.delayed(delay);
      }
    }

    if (kDebugMode) {
      print('❌ Failed to establish connection after $maxRetries attempts');
    }
    return false;
  }

  /// Get detailed connection status for UI display
  static Future<Map<String, dynamic>> getConnectionStatus() async {
    try {
      final url = await _findWorkingUrl();
      if (url != null) {
        // Test actual prediction endpoint
        final isWorking = await _testPredictionEndpoint(url);
        return {
          'connected': true,
          'url': url,
          'message': isWorking
              ? 'API connection is healthy'
              : 'API partially available',
          'prediction_ready': isWorking,
          'last_check': DateTime.now().toIso8601String(),
        };
      } else {
        return {
          'connected': false,
          'url': null,
          'message': 'Cannot connect to API server',
          'prediction_ready': false,
          'last_check': DateTime.now().toIso8601String(),
        };
      }
    } catch (e) {
      return {
        'connected': false,
        'url': null,
        'message': 'Connection error: $e',
        'prediction_ready': false,
        'last_check': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Test if prediction endpoint is working
  static Future<bool> _testPredictionEndpoint(String baseUrl) async {
    try {
      // Create minimal test symptoms
      final testSymptoms = {
        'fever': 0,
        'headache': 0,
        'skin_rash': 0,
        'muscle_pain': 0,
        'weight_loss': 0,
        'fatigue': 0,
        'oral_ulcers': 0,
        'swollen_lymph_nodes': 0,
        'diarrhea': 0,
        'night_sweats': 0,
        'unprotected_sex': 0,
        'shared_needles': 0,
        'multiple_partners': 0,
      };

      final endpoint = baseUrl.contains('5000') ? '/predict' : '/api/predict';
      final response = await http
          .post(
            Uri.parse('$baseUrl$endpoint'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(testSymptoms),
          )
          .timeout(const Duration(seconds: 3));

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
