import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class ServerMonitorService {
  static const Duration _checkInterval = Duration(seconds: 30);
  static const int _maxRetries = 3;

  static Timer? _monitorTimer;
  static bool _isMonitoring = false;
  static int _consecutiveFailures = 0;

  // Server endpoints to monitor
  static const List<Map<String, String>> _servers = [
    {
      'name': 'Flask Backend',
      'url': 'http://localhost:5001',
      'health': '/api/health',
    },
    {'name': 'API Server', 'url': 'http://localhost:5000', 'health': '/health'},
  ];

  /// Start monitoring server health
  static void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _consecutiveFailures = 0;

    if (kDebugMode) {
      print('🔍 Starting server health monitoring...');
    }

    // Initial check
    _checkServerHealth();

    // Schedule periodic checks
    _monitorTimer = Timer.periodic(_checkInterval, (timer) {
      _checkServerHealth();
    });
  }

  /// Stop monitoring
  static void stopMonitoring() {
    _isMonitoring = false;
    _monitorTimer?.cancel();
    _monitorTimer = null;

    if (kDebugMode) {
      print('⏹️ Stopped server health monitoring');
    }
  }

  /// Check health of all servers
  static Future<void> _checkServerHealth() async {
    bool allHealthy = true;

    for (final server in _servers) {
      final isHealthy = await _checkSingleServer(server);
      if (!isHealthy) {
        allHealthy = false;
      }
    }

    if (allHealthy) {
      _consecutiveFailures = 0;
    } else {
      _consecutiveFailures++;

      if (_consecutiveFailures >= _maxRetries) {
        if (kDebugMode) {
          print('❌ Multiple server failures detected. Attempting restart...');
        }
        await _attemptServerRestart();
      }
    }
  }

  /// Check health of a single server
  static Future<bool> _checkSingleServer(Map<String, String> server) async {
    try {
      final url = '${server['url']}${server['health']}';
      final response = await http
          .get(Uri.parse(url))
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'healthy') {
          if (kDebugMode) {
            print('✅ ${server['name']} is healthy');
          }
          return true;
        }
      }

      if (kDebugMode) {
        print(
          '⚠️ ${server['name']} health check failed: ${response.statusCode}',
        );
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ ${server['name']} connection failed: $e');
      }
      return false;
    }
  }

  /// Attempt to restart servers with enhanced retry logic
  static Future<void> _attemptServerRestart() async {
    if (kDebugMode) {
      print('🔄 Attempting server recovery...');
    }

    // Try to reconnect with exponential backoff
    for (int attempt = 1; attempt <= 3; attempt++) {
      if (kDebugMode) {
        print('🔄 Recovery attempt $attempt/3');
      }

      // Wait with exponential backoff
      await Future.delayed(Duration(seconds: attempt * 2));

      // Test connection again
      bool anyHealthy = false;
      for (final server in _servers) {
        final isHealthy = await _checkSingleServer(server);
        if (isHealthy) {
          anyHealthy = true;
          if (kDebugMode) {
            print('✅ ${server['name']} recovered!');
          }
        }
      }

      if (anyHealthy) {
        _consecutiveFailures = 0;
        if (kDebugMode) {
          print('🎉 Server recovery successful!');
        }
        return;
      }
    }

    if (kDebugMode) {
      print('❌ Server recovery failed. Manual intervention may be required:');
      print('   1. cd api && python app.py');
      print('   2. cd flask_backend && python app.py');
    }

    // Reset failure counter to prevent spam
    _consecutiveFailures = 0;
  }

  /// Get current server status
  static Future<Map<String, dynamic>> getServerStatus() async {
    final List<Map<String, dynamic>> serverStatuses = [];
    bool allHealthy = true;

    for (final server in _servers) {
      final isHealthy = await _checkSingleServer(server);
      if (!isHealthy) allHealthy = false;

      serverStatuses.add({
        'name': server['name'],
        'url': server['url'],
        'healthy': isHealthy,
        'lastChecked': DateTime.now().toIso8601String(),
      });
    }

    return {
      'allHealthy': allHealthy,
      'servers': serverStatuses,
      'consecutiveFailures': _consecutiveFailures,
      'isMonitoring': _isMonitoring,
    };
  }

  /// Force a health check now
  static Future<bool> forceHealthCheck() async {
    if (kDebugMode) {
      print('🔍 Forcing immediate health check...');
    }

    await _checkServerHealth();
    final status = await getServerStatus();
    return status['allHealthy'] as bool;
  }

  /// Check if monitoring is active
  static bool get isMonitoring => _isMonitoring;

  /// Get consecutive failure count
  static int get consecutiveFailures => _consecutiveFailures;
}
