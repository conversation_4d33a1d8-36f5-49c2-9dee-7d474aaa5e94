import 'package:flutter/foundation.dart';
import 'firebase_auth_service.dart';
import 'firebase_database_service.dart';
import 'web_safe_auth_service.dart';
import 'web_safe_database_service.dart';

/// Service factory that provides appropriate services based on platform
class ServiceFactory {
  static bool get _isWebWithFirebaseIssues {
    // Check if we're on web and should use web-safe services
    return kIsWeb;
  }

  /// Get the appropriate authentication service
  static dynamic getAuthService() {
    if (_isWebWithFirebaseIssues) {
      return WebSafeAuthService();
    } else {
      return FirebaseAuthService();
    }
  }

  /// Get the appropriate database service
  static dynamic getDatabaseService() {
    if (_isWebWithFirebaseIssues) {
      return WebSafeDatabaseService();
    } else {
      return FirebaseDatabaseService();
    }
  }
}

/// Type-safe wrapper for authentication service
class AuthServiceWrapper {
  final dynamic _service;
  
  AuthServiceWrapper() : _service = ServiceFactory.getAuthService();

  // Delegate all methods to the underlying service
  get currentUser => _service.currentUser;
  Stream get authStateChanges => _service.authStateChanges;
  
  Future getCurrentUser() => _service.getCurrentUser();
  
  Future signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
  }) => _service.signUpWithEmailAndPassword(
    email: email,
    password: password,
    fullName: fullName,
  );
  
  Future signInWithEmailAndPassword({
    required String email,
    required String password,
  }) => _service.signInWithEmailAndPassword(
    email: email,
    password: password,
  );
  
  Future<void> signOut() => _service.signOut();
  Future<void> resetPassword(String email) => _service.resetPassword(email);
  Future<void> deleteAccount() => _service.deleteAccount();
  Future<bool> isEmailRegistered(String email) => _service.isEmailRegistered(email);
}

/// Type-safe wrapper for database service
class DatabaseServiceWrapper {
  final dynamic _service;
  
  DatabaseServiceWrapper() : _service = ServiceFactory.getDatabaseService();

  // Delegate all methods to the underlying service
  Future<String> savePrediction({
    required String userId,
    required String prediction,
    required double confidence,
    required Map<String, int> symptoms,
    Map<String, int>? exposureLocations,
    Map<String, dynamic>? locationWarning,
  }) => _service.savePrediction(
    userId: userId,
    prediction: prediction,
    confidence: confidence,
    symptoms: symptoms,
    exposureLocations: exposureLocations,
    locationWarning: locationWarning,
  );
  
  Future getUserPredictions(String userId) => _service.getUserPredictions(userId);
  Future getRecentPredictions(String userId, {int limit = 5}) => 
      _service.getRecentPredictions(userId, limit: limit);
  Future<void> deletePrediction(String predictionId, String userId) => 
      _service.deletePrediction(predictionId, userId);
  Future getPredictionStats(String userId) => _service.getPredictionStats(userId);
  Future<void> updateUserProfile(dynamic user) => _service.updateUserProfile(user);
  Future getUserProfile(String userId) => _service.getUserProfile(userId);
}
