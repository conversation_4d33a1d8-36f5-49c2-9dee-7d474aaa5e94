import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_settings.dart';
import 'language_service.dart';

class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  static const String _settingsKey = 'app_settings';
  AppSettings _currentSettings = AppSettings();

  AppSettings get currentSettings => _currentSettings;

  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _currentSettings = AppSettings.fromJson(settingsMap);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading settings: $e');
      }
      // Use default settings if loading fails
      _currentSettings = AppSettings();
    }
  }

  Future<bool> updateSettings(AppSettings newSettings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(newSettings.toJson());

      final success = await prefs.setString(_settingsKey, settingsJson);
      if (success) {
        _currentSettings = newSettings;
      }
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving settings: $e');
      }
      return false;
    }
  }

  Future<bool> resetToDefaults() async {
    return await updateSettings(AppSettings());
  }

  // Specific setting updates
  Future<bool> updateNotifications(bool enabled) async {
    return await updateSettings(
      _currentSettings.copyWith(notificationsEnabled: enabled),
    );
  }

  Future<bool> updateTheme(String theme) async {
    return await updateSettings(_currentSettings.copyWith(theme: theme));
  }

  Future<bool> updateLanguage(String language) async {
    // Also update the language service
    final languageService = LanguageService();
    await languageService.setLanguage(language);

    return await updateSettings(_currentSettings.copyWith(language: language));
  }

  Future<bool> updateLocationTracking(bool enabled) async {
    return await updateSettings(
      _currentSettings.copyWith(locationTrackingEnabled: enabled),
    );
  }

  Future<bool> updateDataAnalytics(bool enabled) async {
    return await updateSettings(
      _currentSettings.copyWith(dataAnalyticsEnabled: enabled),
    );
  }

  Future<bool> updateBiometricAuth(bool enabled) async {
    return await updateSettings(
      _currentSettings.copyWith(biometricAuthEnabled: enabled),
    );
  }

  Future<bool> updateReminderFrequency(int hours) async {
    return await updateSettings(
      _currentSettings.copyWith(reminderFrequency: hours),
    );
  }

  Future<bool> updateEmergencyContact(String contact) async {
    return await updateSettings(
      _currentSettings.copyWith(
        emergencyContact: contact,
        emergencyContactsEnabled: contact.isNotEmpty,
      ),
    );
  }

  Future<bool> updateTextSize(double size) async {
    return await updateSettings(_currentSettings.copyWith(textSize: size));
  }

  Future<bool> updateDataRetention(int days) async {
    return await updateSettings(
      _currentSettings.copyWith(dataRetentionDays: days),
    );
  }

  // Export settings as JSON
  String exportSettings() {
    return jsonEncode(_currentSettings.toJson());
  }

  // Import settings from JSON
  Future<bool> importSettings(String settingsJson) async {
    try {
      final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
      final newSettings = AppSettings.fromJson(settingsMap);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        print('Error importing settings: $e');
      }
      return false;
    }
  }

  // Get theme mode for MaterialApp
  String getThemeMode() {
    return _currentSettings.theme;
  }

  // Get text scale factor
  double getTextScaleFactor() {
    return _currentSettings.textSize;
  }

  // Check if feature is enabled
  bool isFeatureEnabled(String feature) {
    switch (feature) {
      case 'notifications':
        return _currentSettings.notificationsEnabled;
      case 'risk_alerts':
        return _currentSettings.riskAlertsEnabled;
      case 'hospital_notifications':
        return _currentSettings.hospitalNotificationsEnabled;
      case 'follow_up_reminders':
        return _currentSettings.followUpRemindersEnabled;
      case 'location_tracking':
        return _currentSettings.locationTrackingEnabled;
      case 'data_analytics':
        return _currentSettings.dataAnalyticsEnabled;
      case 'biometric_auth':
        return _currentSettings.biometricAuthEnabled;
      case 'auto_backup':
        return _currentSettings.autoBackupEnabled;
      case 'share_anonymous_data':
        return _currentSettings.shareAnonymousData;
      case 'emergency_contacts':
        return _currentSettings.emergencyContactsEnabled;
      case 'sound':
        return _currentSettings.soundEnabled;
      case 'vibration':
        return _currentSettings.vibrationEnabled;
      case 'high_contrast':
        return _currentSettings.highContrastMode;
      case 'offline_mode':
        return _currentSettings.offlineModeEnabled;
      case 'export_data':
        return _currentSettings.exportDataEnabled;
      default:
        return false;
    }
  }

  // Get setting value
  dynamic getSettingValue(String setting) {
    switch (setting) {
      case 'theme':
        return _currentSettings.theme;
      case 'language':
        return _currentSettings.language;
      case 'reminder_frequency':
        return _currentSettings.reminderFrequency;
      case 'emergency_contact':
        return _currentSettings.emergencyContact;
      case 'text_size':
        return _currentSettings.textSize;
      case 'data_retention_days':
        return _currentSettings.dataRetentionDays;
      default:
        return null;
    }
  }
}
