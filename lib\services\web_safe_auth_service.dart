import 'package:logger/logger.dart';
import '../models/user.dart' as app_user;

/// Web-safe authentication service that provides mock functionality
/// when Firebase web packages are not available
class WebSafeAuthService {
  final Logger _logger = Logger();
  app_user.User? _currentUser;

  // Mock current user for web testing
  app_user.User? get currentUser => _currentUser;

  // Mock auth state changes stream
  Stream<app_user.User?> get authStateChanges async* {
    yield _currentUser;
  }

  // Mock get current user
  Future<app_user.User?> getCurrentUser() async {
    _logger.i('Web mode: Using mock authentication');
    return _currentUser;
  }

  // Mock sign up
  Future<app_user.User?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
  }) async {
    _logger.i('Web mode: Mock sign up for $email');
    
    // Create a mock user
    _currentUser = app_user.User(
      id: 'web_mock_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      fullName: fullName,
      createdAt: DateTime.now(),
    );
    
    return _currentUser;
  }

  // Mock sign in
  Future<app_user.User?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    _logger.i('Web mode: Mock sign in for $email');
    
    // Create a mock user
    _currentUser = app_user.User(
      id: 'web_mock_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      fullName: 'Web Test User',
      createdAt: DateTime.now(),
    );
    
    return _currentUser;
  }

  // Mock sign out
  Future<void> signOut() async {
    _logger.i('Web mode: Mock sign out');
    _currentUser = null;
  }

  // Mock reset password
  Future<void> resetPassword(String email) async {
    _logger.i('Web mode: Mock password reset for $email');
    // In web mode, just log the action
  }

  // Mock delete account
  Future<void> deleteAccount() async {
    _logger.i('Web mode: Mock account deletion');
    _currentUser = null;
  }

  // Mock email check
  Future<bool> isEmailRegistered(String email) async {
    _logger.i('Web mode: Mock email check for $email');
    // Always return false for web testing
    return false;
  }
}
