import 'package:logger/logger.dart';
import '../models/prediction_result.dart';
import '../models/user.dart' as app_user;

/// Web-safe database service that provides mock functionality
/// when Firebase web packages are not available
class WebSafeDatabaseService {
  final Logger _logger = Logger();

  // Mock in-memory storage for web testing
  final List<PredictionResult> _mockPredictions = [];
  final Map<String, app_user.User> _mockUsers = {};

  // Mock save prediction
  Future<String> savePrediction({
    required String userId,
    required String prediction,
    required double confidence,
    required Map<String, int> symptoms,
    Map<String, int>? exposureLocations,
    Map<String, dynamic>? locationWarning,
  }) async {
    _logger.i('Web mode: Mock saving prediction for user $userId');

    final predictionId = 'web_pred_${DateTime.now().millisecondsSinceEpoch}';

    // Convert symptoms to the expected format
    final symptomsMap = Map<String, dynamic>.from(symptoms);

    // Convert exposureLocations to the expected format
    final exposureLocationsMap = exposureLocations?.map(
      (key, value) => MapEntry(key, value.toString()),
    );

    // Generate mock recommendations based on prediction
    final recommendations = _generateMockRecommendations(prediction);

    final predictionResult = PredictionResult(
      id: predictionId,
      userId: userId,
      prediction: prediction,
      confidence: confidence,
      symptoms: symptomsMap,
      riskLevel: prediction, // Use prediction as risk level
      recommendations: recommendations,
      exposureLocations: exposureLocationsMap,
      locationWarning: locationWarning,
      createdAt: DateTime.now(),
    );

    _mockPredictions.add(predictionResult);
    return predictionId;
  }

  // Mock get user predictions
  Future<List<PredictionResult>> getUserPredictions(String userId) async {
    _logger.i('Web mode: Mock getting predictions for user $userId');

    return _mockPredictions.where((pred) => pred.userId == userId).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Mock get recent predictions
  Future<List<PredictionResult>> getRecentPredictions(
    String userId, {
    int limit = 5,
  }) async {
    _logger.i('Web mode: Mock getting recent predictions for user $userId');

    final userPredictions = await getUserPredictions(userId);
    return userPredictions.take(limit).toList();
  }

  // Mock delete prediction
  Future<void> deletePrediction(String predictionId, String userId) async {
    _logger.i('Web mode: Mock deleting prediction $predictionId');

    _mockPredictions.removeWhere(
      (pred) => pred.id == predictionId && pred.userId == userId,
    );
  }

  // Mock get prediction statistics
  Future<Map<String, dynamic>> getPredictionStats(String userId) async {
    _logger.i('Web mode: Mock getting prediction stats for user $userId');

    final userPredictions = await getUserPredictions(userId);

    final riskDistribution = <String, int>{};
    DateTime? lastPredictionDate;

    for (final pred in userPredictions) {
      riskDistribution[pred.prediction] =
          (riskDistribution[pred.prediction] ?? 0) + 1;

      if (lastPredictionDate == null ||
          pred.createdAt.isAfter(lastPredictionDate)) {
        lastPredictionDate = pred.createdAt;
      }
    }

    return {
      'totalPredictions': userPredictions.length,
      'riskDistribution': riskDistribution,
      'lastPredictionDate': lastPredictionDate,
    };
  }

  // Mock update user profile
  Future<void> updateUserProfile(app_user.User user) async {
    _logger.i('Web mode: Mock updating user profile for ${user.id}');
    _mockUsers[user.id] = user;
  }

  // Mock get user profile
  Future<app_user.User?> getUserProfile(String userId) async {
    _logger.i('Web mode: Mock getting user profile for $userId');
    return _mockUsers[userId];
  }

  // Helper method to generate mock recommendations
  List<String> _generateMockRecommendations(String prediction) {
    switch (prediction.toLowerCase()) {
      case 'high':
        return [
          'Consult a healthcare provider immediately',
          'Get tested for HIV as soon as possible',
          'Avoid unprotected sexual contact',
          'Consider PEP (Post-Exposure Prophylaxis) if recent exposure',
        ];
      case 'medium':
        return [
          'Schedule an appointment with a healthcare provider',
          'Consider getting tested for HIV',
          'Practice safe sex',
          'Monitor symptoms closely',
        ];
      case 'low':
        return [
          'Continue practicing safe behaviors',
          'Regular health checkups are recommended',
          'Stay informed about HIV prevention',
          'Consider routine testing if sexually active',
        ];
      default:
        return [
          'Consult with a healthcare professional',
          'Follow general health guidelines',
        ];
    }
  }
}
