import 'package:flutter/material.dart';
import '../services/prediction_service.dart';

class ConnectionStatusWidget extends StatefulWidget {
  final bool showDetails;
  final VoidCallback? onRetry;

  const ConnectionStatusWidget({
    super.key,
    this.showDetails = false,
    this.onRetry,
  });

  @override
  State<ConnectionStatusWidget> createState() => _ConnectionStatusWidgetState();
}

class _ConnectionStatusWidgetState extends State<ConnectionStatusWidget> {
  bool _isConnected = false;
  bool _isChecking = true;
  String _message = '';
  String _lastCheck = '';

  @override
  void initState() {
    super.initState();
    _checkConnection();
  }

  Future<void> _checkConnection() async {
    setState(() {
      _isChecking = true;
    });

    try {
      final status = await HivPredictionService.getConnectionStatus();
      if (mounted) {
        setState(() {
          _isConnected = status['connected'] ?? false;
          _message = status['message'] ?? 'Unknown status';
          _lastCheck = status['last_check'] ?? '';
          _isChecking = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isConnected = false;
          _message = 'Connection check failed: $e';
          _isChecking = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showDetails) {
      return _buildDetailedStatus();
    } else {
      return _buildSimpleIndicator();
    }
  }

  Widget _buildSimpleIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _isConnected
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isConnected ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_isChecking)
            const SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          else
            Icon(
              _isConnected ? Icons.wifi : Icons.wifi_off,
              size: 16,
              color: _isConnected ? Colors.green : Colors.red,
            ),
          const SizedBox(width: 4),
          Text(
            _isConnected ? 'Online' : 'Offline',
            style: TextStyle(
              fontSize: 12,
              color: _isConnected ? Colors.green : Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedStatus() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _isConnected
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isConnected ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (_isChecking)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                Icon(
                  _isConnected ? Icons.check_circle : Icons.error,
                  color: _isConnected ? Colors.green : Colors.red,
                  size: 20,
                ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _isConnected ? 'API Connected' : 'Connection Issue',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _isConnected ? Colors.green[700] : Colors.red[700],
                    fontSize: 16,
                  ),
                ),
              ),
              if (!_isConnected && widget.onRetry != null)
                TextButton(
                  onPressed: () {
                    _checkConnection();
                    widget.onRetry?.call();
                  },
                  child: const Text('Retry'),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _message,
            style: TextStyle(
              color: _isConnected ? Colors.green[600] : Colors.red[600],
              fontSize: 14,
            ),
          ),
          if (_lastCheck.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'Last checked: ${_formatTime(_lastCheck)}',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inSeconds < 60) {
        return 'Just now';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else {
        return '${difference.inHours}h ago';
      }
    } catch (e) {
      return 'Unknown';
    }
  }
}

class ConnectionStatusBanner extends StatelessWidget {
  final bool isConnected;
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;

  const ConnectionStatusBanner({
    super.key,
    required this.isConnected,
    required this.message,
    this.onRetry,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    if (isConnected) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        border: Border(bottom: BorderSide(color: Colors.red[200]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.warning, color: Colors.red[700], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Connection Issue',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                    fontSize: 14,
                  ),
                ),
                Text(
                  message,
                  style: TextStyle(color: Colors.red[600], fontSize: 12),
                ),
              ],
            ),
          ),
          if (onRetry != null)
            TextButton(onPressed: onRetry, child: const Text('Retry')),
          if (onDismiss != null)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: onDismiss,
              iconSize: 18,
            ),
        ],
      ),
    );
  }
}

class PersistentConnectionMonitor extends StatefulWidget {
  final Widget child;

  const PersistentConnectionMonitor({super.key, required this.child});

  @override
  State<PersistentConnectionMonitor> createState() =>
      _PersistentConnectionMonitorState();
}

class _PersistentConnectionMonitorState
    extends State<PersistentConnectionMonitor> {
  bool _isConnected = true;
  String _message = '';
  bool _showBanner = false;

  @override
  void initState() {
    super.initState();
    _startMonitoring();
  }

  void _startMonitoring() {
    // Check connection every 30 seconds
    Stream.periodic(const Duration(seconds: 30)).listen((_) {
      _checkConnection();
    });

    // Initial check
    _checkConnection();
  }

  Future<void> _checkConnection() async {
    try {
      final status = await HivPredictionService.getConnectionStatus();
      final connected = status['connected'] ?? false;
      final message = status['message'] ?? '';

      if (mounted && connected != _isConnected) {
        setState(() {
          _isConnected = connected;
          _message = message;
          _showBanner = !connected;
        });
      }
    } catch (e) {
      if (mounted && _isConnected) {
        setState(() {
          _isConnected = false;
          _message = 'Connection monitoring failed';
          _showBanner = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ConnectionStatusBanner(
          isConnected: _isConnected || !_showBanner,
          message: _message,
          onRetry: _checkConnection,
          onDismiss: () {
            setState(() {
              _showBanner = false;
            });
          },
        ),
        Expanded(child: widget.child),
      ],
    );
  }
}
