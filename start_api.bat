@echo off
echo ========================================
echo   HIV Predictor - Starting Your AI API
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python is installed
echo.

REM Navigate to API directory
cd api

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment for API...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install requirements
echo 📥 Installing API requirements...
pip install -r ..\requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)

echo ✅ Requirements installed
echo.

REM Start your API server
echo 🚀 Starting Your HIV Predictor AI API...
echo.
echo Your API will be available at:
echo   🌐 http://localhost:5000
echo   🔍 Health check: http://localhost:5000/health
echo   🧠 Prediction: http://localhost:5000/predict
echo.
echo 💡 The Flutter app will automatically detect your API
echo 📱 You'll see "🤖 Your AI API is connected" in the app
echo.
echo Press Ctrl+C to stop the API server
echo.

python app.py

echo.
echo 👋 API server stopped
pause
