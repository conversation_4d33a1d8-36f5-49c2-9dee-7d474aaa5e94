@echo off
echo ========================================
echo   HIV Predictor Backend Server Setup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python is installed
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install requirements
echo 📥 Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)

echo ✅ Requirements installed
echo.

REM Start the server
echo 🚀 Starting HIV Predictor Backend Server...
echo.
echo Server will be available at:
echo   🌐 http://localhost:5001
echo   🔍 Health check: http://localhost:5001/api/health
echo   🧪 Test endpoint: http://localhost:5001/api/test
echo.
echo Press Ctrl+C to stop the server
echo.

python flask_server_template.py

echo.
echo 👋 Server stopped
pause
