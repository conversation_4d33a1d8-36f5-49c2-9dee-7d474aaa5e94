#!/bin/bash

echo "========================================"
echo "  HIV Predictor Backend Server Setup"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed"
    echo "Please install Python 3.8+ from https://python.org"
    exit 1
fi

echo "✅ Python is installed"
echo

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create virtual environment"
        exit 1
    fi
    echo "✅ Virtual environment created"
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "📥 Installing requirements..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Failed to install requirements"
    exit 1
fi

echo "✅ Requirements installed"
echo

# Start the server
echo "🚀 Starting HIV Predictor Backend Server..."
echo
echo "Server will be available at:"
echo "  🌐 http://localhost:5001"
echo "  🔍 Health check: http://localhost:5001/api/health"
echo "  🧪 Test endpoint: http://localhost:5001/api/test"
echo
echo "Press Ctrl+C to stop the server"
echo

python flask_server_template.py

echo
echo "👋 Server stopped"
