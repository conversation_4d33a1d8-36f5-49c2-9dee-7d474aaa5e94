@echo off
echo ========================================
echo   HIV Predictor App - Server Startup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

echo ✅ Python is installed
echo.

REM Check if PostgreSQL is configured
if not exist "flask_backend\.env" (
    echo ⚠️  PostgreSQL not configured yet!
    echo Please run: python setup_postgresql.py
    echo.
    pause
    exit /b 1
)

REM Install PostgreSQL dependencies if needed
echo 📦 Checking PostgreSQL dependencies...
pip show psycopg2-binary >nul 2>&1
if errorlevel 1 (
    echo Installing PostgreSQL dependencies...
    pip install psycopg2-binary python-dotenv
)

REM Initialize database if needed
echo 🔧 Initializing PostgreSQL database...
cd flask_backend
python init_database.py
cd ..

REM Function to start API server
echo 🚀 Starting API Server (Port 5000)...
start "API Server" cmd /k "cd /d api && python app.py"
timeout /t 3 >nul

REM Function to start Flask backend with PostgreSQL
echo 🚀 Starting Flask Backend with PostgreSQL (Port 5001)...
start "Flask Backend" cmd /k "cd /d flask_backend && python app.py"
timeout /t 3 >nul

echo.
echo ⏳ Waiting for servers to start...
timeout /t 5 >nul

REM Test server connectivity
echo.
echo 🧪 Testing server connectivity...

REM Test API Server
echo Testing API Server (Port 5000)...
curl -s http://localhost:5000/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  API Server may not be ready yet
) else (
    echo ✅ API Server is responding
)

REM Test Flask Backend
echo Testing Flask Backend (Port 5001)...
curl -s http://localhost:5001/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Flask Backend may not be ready yet
) else (
    echo ✅ Flask Backend is responding
)

echo.
echo ========================================
echo   Server Startup Complete!
echo ========================================
echo.
echo 📊 Server Status:
echo   • API Server:     http://localhost:5000
echo   • Flask Backend:  http://localhost:5001
echo.
echo 💡 Tips:
echo   • Keep this window open to monitor server status
echo   • Close server windows to stop the servers
echo   • Run this script again if servers disconnect
echo.
echo 🔄 Monitoring server health every 30 seconds...
echo Press Ctrl+C to stop monitoring
echo.

REM Monitor server health
:monitor_loop
timeout /t 30 >nul

echo [%date% %time%] Checking server health...

REM Check API Server
curl -s http://localhost:5000/health >nul 2>&1
if errorlevel 1 (
    echo ❌ API Server is down! Attempting restart...
    start "API Server" cmd /k "cd /d api && python app.py"
) else (
    echo ✅ API Server OK
)

REM Check Flask Backend
curl -s http://localhost:5001/api/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Flask Backend is down! Attempting restart...
    start "Flask Backend" cmd /k "cd /d flask_backend && python app.py"
) else (
    echo ✅ Flask Backend OK
)

goto monitor_loop
