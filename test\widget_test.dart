import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hiv_predictor_app/screens/main_navigation_screen.dart';

void main() {
  testWidgets('App smoke test', (WidgetTester tester) async {
    // Test the main navigation screen directly without connection manager
    await tester.pumpWidget(const MaterialApp(home: MainNavigationScreen()));

    // Wait for the widget to render
    await tester.pump();

    // Check if the MainNavigationScreen appears
    expect(find.byType(MainNavigationScreen), findsOneWidget);

    // Check if we can find some basic navigation elements
    expect(find.byType(BottomNavigationBar), findsOneWidget);
  });
}
